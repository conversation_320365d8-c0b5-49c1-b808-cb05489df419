<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

// Get the request data
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data || !isset($data['filename']) || !isset($data['content'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid request data']);
    exit();
}

$filename = $data['filename'];
$content = $data['content'];

// Validate filename to prevent directory traversal
$allowedFiles = [
    'feedbacks.json',
    'checkins.json',
    'members.json',
    'activities.json',
    'courses.json',
    'navigation.json'
];

if (!in_array($filename, $allowedFiles)) {
    http_response_code(403);
    echo json_encode(['error' => 'File not allowed']);
    exit();
}

$filepath = '../data/' . $filename;

// Create data directory if it doesn't exist
$dataDir = dirname($filepath);
if (!is_dir($dataDir)) {
    mkdir($dataDir, 0755, true);
}

// Save the file
$result = file_put_contents($filepath, json_encode($content, JSON_PRETTY_PRINT));

if ($result === false) {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to save file']);
    exit();
}

echo json_encode(['success' => true, 'message' => 'File saved successfully']);
?>
