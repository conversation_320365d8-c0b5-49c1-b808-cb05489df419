# SnowNavi API

This directory contains API endpoints for saving data files in the SnowNavi admin system.

## Setup Options

### Option 1: PHP Server (Recommended)

If your server supports PHP:

1. Ensure PHP is enabled on your web server
2. The `save-data.php` file should work automatically
3. The `.htaccess` file handles CORS headers

### Option 2: Node.js Server

If you prefer Node.js:

1. Install Node.js on your server
2. Run the API server:
   ```bash
   cd api
   node save-data.js
   ```
3. The server will run on port 3001 by default

### Option 3: Static Server (Fallback)

If neither PHP nor Node.js is available:

1. The system will automatically fall back to localStorage
2. Data will be saved locally in the browser
3. A warning message will be shown to users
4. Data may not persist across sessions or devices

## Security

- Only specific JSON files are allowed to be saved
- Directory traversal protection is implemented
- CORS headers are properly configured

## Files

- `save-data.php` - PHP API endpoint
- `save-data.js` - Node.js API endpoint  
- `.htaccess` - Apache configuration for PHP
- `README.md` - This documentation

## Testing

You can test the API by making a POST request to `/api/save-data.php` or `/api/save-data.js` with:

```json
{
  "filename": "feedbacks.json",
  "content": { "test": "data" }
}
```

The API will respond with:

```json
{
  "success": true,
  "message": "File saved successfully"
}
```
