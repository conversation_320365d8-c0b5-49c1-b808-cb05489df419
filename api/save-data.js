// Node.js API for saving data files
const fs = require('fs').promises;
const path = require('path');

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Content-Type': 'application/json'
};

// Allowed files for security
const allowedFiles = [
  'feedbacks.json',
  'checkins.json',
  'members.json',
  'activities.json',
  'courses.json',
  'navigation.json'
];

async function handleRequest(req, res) {
  // Set CORS headers
  Object.entries(corsHeaders).forEach(([key, value]) => {
    res.setHeader(key, value);
  });

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  if (req.method !== 'POST') {
    res.writeHead(405);
    res.end(JSON.stringify({ error: 'Method not allowed' }));
    return;
  }

  try {
    // Parse request body
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', async () => {
      try {
        const data = JSON.parse(body);
        
        if (!data.filename || !data.content) {
          res.writeHead(400);
          res.end(JSON.stringify({ error: 'Invalid request data' }));
          return;
        }

        // Validate filename
        if (!allowedFiles.includes(data.filename)) {
          res.writeHead(403);
          res.end(JSON.stringify({ error: 'File not allowed' }));
          return;
        }

        // Save file
        const filepath = path.join(__dirname, '..', 'data', data.filename);
        
        // Ensure data directory exists
        const dataDir = path.dirname(filepath);
        await fs.mkdir(dataDir, { recursive: true });
        
        // Write file
        await fs.writeFile(filepath, JSON.stringify(data.content, null, 2));
        
        res.writeHead(200);
        res.end(JSON.stringify({ success: true, message: 'File saved successfully' }));
        
      } catch (error) {
        console.error('Error processing request:', error);
        res.writeHead(500);
        res.end(JSON.stringify({ error: 'Internal server error' }));
      }
    });

  } catch (error) {
    console.error('Error handling request:', error);
    res.writeHead(500);
    res.end(JSON.stringify({ error: 'Internal server error' }));
  }
}

// Export for use with Express or other frameworks
module.exports = handleRequest;

// If running directly
if (require.main === module) {
  const http = require('http');
  const server = http.createServer(handleRequest);
  
  const port = process.env.PORT || 3001;
  server.listen(port, () => {
    console.log(`API server running on port ${port}`);
  });
}
