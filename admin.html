<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>SnowNavi Admin Panel</title>

  <!-- Favicon -->
  <link rel="icon" type="image/png" sizes="32x32" href="assets/picture/snownavi_logo.png">
  <link rel="icon" type="image/png" sizes="16x16" href="assets/picture/snownavi_logo.png">
  <link rel="shortcut icon" href="assets/picture/snownavi_logo.png">

  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      background: #f7f7f7;
      height: 100vh;
      display: flex;
      flex-direction: column;
    }
    h1 {
      color: #E53512;
      padding: 1rem;
      margin: 0;
      text-align: center;
    }
    .admin-container {
      max-width: 800px;
      margin: 2rem auto;
      padding: 2rem;
      background: white;
      border-radius: 8px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
    .admin-options {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-top: 2rem;
      max-width: 1200px;
      margin-left: auto;
      margin-right: auto;
    }
    .admin-card {
      background: #f9f9f9;
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 1.5rem;
      text-align: center;
      transition: transform 0.2s, box-shadow 0.2s;
      cursor: pointer;
    }
    .admin-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    .admin-card h2 {
      color: #E53512;
      margin-top: 0;
    }
    .admin-card p {
      color: #666;
      margin-bottom: 1.5rem;
    }
    .admin-btn {
      background: #E53512;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      border-radius: 5px;
      cursor: pointer;
      text-decoration: none;
      display: inline-block;
    }
    .admin-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
      color: #E53512;
    }
    /* Authentication styles */
    .auth-container {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.9);
      z-index: 1000;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }
    .auth-message {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      text-align: center;
      max-width: 400px;
    }
    .auth-message h2 {
      color: #E53512;
      margin-top: 0;
    }
    .auth-btn {
      background: #E53512;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      border-radius: 5px;
      cursor: pointer;
      margin-top: 1rem;
      text-decoration: none;
      display: inline-block;
    }
    .user-info {
      display: flex;
      align-items: center;
      position: absolute;
      top: 1rem;
      right: 1rem;
    }
    .user-info img {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      margin-right: 0.5rem;
    }
    .logout-btn {
      background: none;
      border: none;
      color: #E53512;
      cursor: pointer;
      margin-left: 1rem;
      text-decoration: underline;
    }

    /* Mobile responsive styles */
    @media (max-width: 768px) {
      body {
        padding: 0.5rem;
      }

      h1 {
        font-size: 1.8rem;
        text-align: center;
        margin: 1rem 0;
      }

      .admin-container {
        margin: 1rem auto;
        padding: 1rem;
        max-width: 100%;
      }

      .admin-options {
        grid-template-columns: 1fr;
        gap: 1rem;
        margin-top: 1rem;
      }

      .admin-card {
        padding: 1rem;
      }

      .admin-icon {
        font-size: 2.5rem;
        margin-bottom: 0.5rem;
      }

      .admin-card h2 {
        font-size: 1.2rem;
        margin: 0.5rem 0;
      }

      .admin-card p {
        font-size: 0.9rem;
        margin-bottom: 1rem;
      }

      .user-info {
        position: static;
        justify-content: center;
        margin-bottom: 1rem;
        padding: 0.5rem;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
      }

      .user-info span {
        font-size: 0.9rem;
      }

      .logout-btn {
        margin-left: 0.5rem;
        font-size: 0.8rem;
      }

      .auth-message {
        margin: 1rem;
        padding: 1.5rem;
        max-width: calc(100% - 2rem);
      }
    }

    @media (max-width: 480px) {
      .admin-container {
        padding: 0.5rem;
      }

      .admin-card {
        padding: 0.75rem;
      }

      .admin-icon {
        font-size: 2rem;
      }

      .admin-card h2 {
        font-size: 1.1rem;
      }

      .admin-card p {
        font-size: 0.8rem;
      }

      .admin-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
      }
    }
  </style>
</head>
<body>
  <!-- Authentication overlay -->
  <div class="auth-container" id="auth-container">
    <div class="auth-message">
      <h2>Authentication Required</h2>
      <p>You need to be logged in to access this page.</p>
      <a href="login.html" class="auth-btn">Go to Login</a>
    </div>
  </div>

  <div class="user-info" id="user-info"></div>

  <h1>SnowNavi Admin Panel</h1>

  <div class="admin-container">
    <h2>Welcome to the SnowNavi Administration Panel</h2>
    <p>Please select the section you would like to manage:</p>

    <div class="admin-options">
      <div class="admin-card" onclick="window.location.href='course_admin.html'">
        <div class="admin-icon">📚</div>
        <h2>Course Management</h2>
        <p>Manage course information, subcourses, and related content.</p>
        <button class="admin-btn">Manage Courses</button>
      </div>

      <div class="admin-card" onclick="window.location.href='member_admin.html'">
        <div class="admin-icon">👥</div>
        <h2>Member Management</h2>
        <p>Manage member information, membership status, and validity periods.</p>
        <button class="admin-btn">Manage Members</button>
      </div>

      <div class="admin-card" onclick="window.location.href='navigation_admin.html'">
        <div class="admin-icon">🧭</div>
        <h2>Navigation Management</h2>
        <p>Configure the website navigation bar and menu items.</p>
        <button class="admin-btn">Manage Navigation</button>
      </div>

      <div class="admin-card" onclick="window.location.href='activity_admin.html'">
        <div class="admin-icon">📅</div>
        <h2>Activity Management</h2>
        <p>Manage activities, events, and course schedules.</p>
        <button class="admin-btn">Manage Activities</button>
      </div>

      <div class="admin-card" onclick="window.location.href='checkin_admin.html'">
        <div class="admin-icon">✅</div>
        <h2>Check-in Management</h2>
        <p>Scan QR codes to check in members and coaches for activities.</p>
        <button class="admin-btn">Manage Check-ins</button>
      </div>

      <div class="admin-card" onclick="window.location.href='feedback_template_admin.html'">
        <div class="admin-icon">📝</div>
        <h2>Feedback Templates</h2>
        <p>Create and manage feedback templates for different course types and skill levels.</p>
        <button class="admin-btn">Manage Templates</button>
      </div>
    </div>
  </div>

  <script>
    // Fetch configuration and check authentication
    async function checkAuth() {
      try {
        // Fetch the authorized email from the server
        const response = await fetch('/api/config');
        if (!response.ok) {
          throw new Error('Failed to fetch configuration');
        }

        const config = await response.json();

        // Parse allowed emails - try new format first, fallback to old format
        let allowedEmails = [];
        if (config.allowedEmails && Array.isArray(config.allowedEmails)) {
          allowedEmails = config.allowedEmails;
        } else if (config.authorizedEmail) {
          allowedEmails = config.authorizedEmail.split(',').map(email => email.trim());
        }

        const auth = JSON.parse(localStorage.getItem('snownavi_auth') || '{}');
        const authContainer = document.getElementById('auth-container');
        const userInfoContainer = document.getElementById('user-info');

        // Check if auth exists, is not expired, and user is authorized
        const isAuthorized = allowedEmails.includes(auth.email);
        if (isAuthorized && auth.expiresAt && auth.expiresAt > Date.now()) {
          // User is authenticated
          authContainer.style.display = 'none';

          // Display user info
          userInfoContainer.innerHTML = `
            <img src="${auth.picture}" alt="Profile">
            <span>${auth.name}</span>
            <button class="logout-btn" onclick="logout()">Logout</button>
          `;
        } else {
          // User is not authenticated, show auth container
          authContainer.style.display = 'flex';
          userInfoContainer.innerHTML = '';
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
        document.getElementById('auth-container').style.display = 'flex';
        document.querySelector('.auth-message p').textContent = 'Error loading configuration. Please try again later.';
      }
    }

    // Logout function
    function logout() {
      localStorage.removeItem('snownavi_auth');
      window.location.href = 'login.html';
    }

    // Initialize the page
    window.onload = checkAuth;
  </script>
</body>
</html>
