<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Feedback Loading Demo - <PERSON>Navi</title>
    <style>
        :root {
            --main-red: #E53512;
            --bg-light: #F9F4F3;
            --text-dark: #2F2F2F;
            --text-gray: #717171;
            --contrast-white: #FFFFFF;
            --accent-blue: #9ED4E7;
            --success-green: #28a745;
            --warning-orange: #ffc107;
        }

        body { 
            font-family: 'Noto Sans SC', sans-serif; 
            margin: 2rem; 
            background: var(--bg-light);
            color: var(--text-dark);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--contrast-white);
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 3px solid var(--main-red);
        }
        h1 { color: var(--main-red); }
        .demo-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid var(--main-red);
            background: var(--bg-light);
        }
        .demo-section h3 {
            color: var(--main-red);
            margin: 0 0 1rem 0;
        }
        .workflow-step {
            display: flex;
            align-items: flex-start;
            margin: 1rem 0;
            padding: 1rem;
            background: var(--contrast-white);
            border-radius: 8px;
            border-left: 4px solid var(--success-green);
        }
        .step-number {
            background: var(--main-red);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
            flex-shrink: 0;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 1rem 0;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        .feature-card {
            background: var(--contrast-white);
            border-radius: 8px;
            padding: 1.5rem;
            border: 2px solid var(--accent-blue);
            transition: transform 0.2s ease;
        }
        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }
        .feature-card h4 {
            color: var(--main-red);
            margin-bottom: 1rem;
        }
        .btn {
            background: var(--main-red);
            color: var(--contrast-white);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 0.5rem;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #c42e0f;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(229, 53, 18, 0.3);
        }
        .btn-secondary {
            background: var(--text-gray);
        }
        .btn-secondary:hover {
            background: #5a5a5a;
        }
        .highlight {
            background: rgba(229, 53, 18, 0.1);
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            font-weight: bold;
            color: var(--main-red);
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 0.75rem;
            text-align: left;
        }
        .comparison-table th {
            background: var(--main-red);
            color: white;
        }
        .comparison-table tr:nth-child(even) {
            background: var(--bg-light);
        }
        .status-badge {
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .badge-implemented {
            background: var(--success-green);
            color: white;
        }
        .badge-new {
            background: var(--warning-orange);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ Dynamic Feedback Loading System</h1>
            <p>Real-time template loading and automatic form generation for student feedback</p>
        </div>
        
        <div class="demo-section">
            <h3>🎯 System Overview</h3>
            <p>The dynamic feedback loading system ensures that feedback forms are always generated using the latest template configuration. When administrators modify feedback templates, the changes are immediately reflected in the feedback forms without requiring page refreshes or system restarts.</p>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🔄 Real-time Template Loading</h4>
                    <p>Templates are loaded dynamically when activities are selected or feedback forms are opened.</p>
                    <ul>
                        <li>Automatic template detection per activity</li>
                        <li>Cache-busting for latest content</li>
                        <li>Fallback to default structure</li>
                        <li>Error handling and user feedback</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4>📝 Dynamic Form Generation</h4>
                    <p>Feedback forms are generated on-the-fly based on the selected activity's template.</p>
                    <ul>
                        <li>Custom skill sections and items</li>
                        <li>Multi-language support</li>
                        <li>Progress tracking per section</li>
                        <li>Flexible assessment criteria</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4>🔧 Template Refresh Mechanism</h4>
                    <p>Manual and automatic refresh capabilities ensure templates are always up-to-date.</p>
                    <ul>
                        <li>Manual refresh button</li>
                        <li>Automatic reload on activity change</li>
                        <li>Template validation</li>
                        <li>Graceful error handling</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>🔄 Dynamic Loading Workflow</h3>
            
            <div class="workflow-step">
                <div class="step-number">1</div>
                <div>
                    <strong>Activity Selection</strong><br>
                    When an activity is selected, the system automatically loads the associated feedback template from the server.
                    <div class="code-block">
function loadActivityTemplate() {
  const activity = activities[selectedActivity];
  const templateId = activity.feedbackTemplateId;
  
  if (feedbackTemplates[templateId]) {
    currentActivityTemplate = feedbackTemplates[templateId];
    console.log(`Loaded template: ${currentActivityTemplate.name.en}`);
  }
}
                    </div>
                </div>
            </div>
            
            <div class="workflow-step">
                <div class="step-number">2</div>
                <div>
                    <strong>Template Validation</strong><br>
                    The system validates that the template exists and has the correct structure before proceeding.
                    <div class="code-block">
if (!feedbackTemplates[templateId]) {
  console.warn(`Feedback template ${templateId} not found`);
  return; // Use default structure
}
                    </div>
                </div>
            </div>
            
            <div class="workflow-step">
                <div class="step-number">3</div>
                <div>
                    <strong>Dynamic Form Generation</strong><br>
                    The feedback form is generated dynamically based on the template's sections and skills.
                    <div class="code-block">
function generateDynamicFeedbackForm() {
  const sections = currentActivityTemplate.sections || {};
  
  // Sort sections by order
  const sortedSections = Object.entries(sections).sort((a, b) => {
    return (a[1].order || 0) - (b[1].order || 0);
  });
  
  // Generate HTML for each section and skill
  return sectionsHTML;
}
                    </div>
                </div>
            </div>
            
            <div class="workflow-step">
                <div class="step-number">4</div>
                <div>
                    <strong>Form Initialization</strong><br>
                    Event listeners are attached to the dynamically generated form elements for interactivity.
                    <div class="code-block">
// Initialize skill assessment with new dynamic form
initializeSkillAssessment();

// Load existing feedback data if available
if (existingFeedback) {
  loadSkillAssessmentData(existingFeedback);
}
                    </div>
                </div>
            </div>
            
            <div class="workflow-step">
                <div class="step-number">5</div>
                <div>
                    <strong>Template Refresh</strong><br>
                    Templates can be refreshed manually or automatically to ensure the latest content is used.
                    <div class="code-block">
async function reloadFeedbackTemplates() {
  const templatesRes = await fetch('data/feedback_templates.json?t=' + Date.now());
  if (templatesRes.ok) {
    feedbackTemplates = await templatesRes.json();
    loadActivityTemplate(); // Reload current template
  }
}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>🔧 Key Implementation Features</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Feature</th>
                        <th>Implementation</th>
                        <th>Benefit</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Template Loading</td>
                        <td>Dynamic fetch on activity selection</td>
                        <td>Always uses latest template</td>
                        <td><span class="status-badge badge-implemented">✅ Implemented</span></td>
                    </tr>
                    <tr>
                        <td>Form Generation</td>
                        <td>HTML generation from template data</td>
                        <td>Flexible form structure</td>
                        <td><span class="status-badge badge-implemented">✅ Implemented</span></td>
                    </tr>
                    <tr>
                        <td>Cache Busting</td>
                        <td>Timestamp parameter in requests</td>
                        <td>Prevents stale data</td>
                        <td><span class="status-badge badge-implemented">✅ Implemented</span></td>
                    </tr>
                    <tr>
                        <td>Error Handling</td>
                        <td>Fallback to default structure</td>
                        <td>System reliability</td>
                        <td><span class="status-badge badge-implemented">✅ Implemented</span></td>
                    </tr>
                    <tr>
                        <td>Manual Refresh</td>
                        <td>Refresh button in UI</td>
                        <td>Admin control</td>
                        <td><span class="status-badge badge-implemented">✅ Implemented</span></td>
                    </tr>
                    <tr>
                        <td>Data Preservation</td>
                        <td>Form data backup during regeneration</td>
                        <td>No data loss</td>
                        <td><span class="status-badge badge-implemented">✅ Implemented</span></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="demo-section">
            <h3>📊 Template Information Display</h3>
            <p>The feedback form now displays comprehensive template information to help instructors understand which assessment framework they're using:</p>
            
            <div class="code-block">
// Template information shown in feedback modal
if (currentActivityTemplate) {
  templateInfo = `
    <p><strong>Feedback Template:</strong> 
       ${currentActivityTemplate.name.en} 
       (${currentActivityTemplate.sport} - ${currentActivityTemplate.targetLevel})
    </p>`;
} else if (activity && activity.feedbackTemplateId) {
  templateInfo = `
    <p><strong>Template:</strong> 
       <span style="color: #dc3545;">Template "${activity.feedbackTemplateId}" not found</span>
    </p>`;
} else {
  templateInfo = `
    <p><strong>Template:</strong> 
       <span style="color: #ffc107;">No template assigned</span>
    </p>`;
}
            </div>
        </div>
        
        <div style="text-align: center; margin: 2rem 0;">
            <h3 style="color: var(--main-red); margin-bottom: 1rem;">🧪 Test Dynamic Feedback Loading</h3>
            <a href="checkin_admin.html" target="_blank" class="btn">✅ Check-in Management</a>
            <a href="feedback_template_admin.html" target="_blank" class="btn">📝 Template Management</a>
            <a href="activity_admin.html" target="_blank" class="btn">📅 Activity Management</a>
            <a href="dynamic_feedback_demo.html" target="_blank" class="btn btn-secondary">🎯 System Overview</a>
        </div>
        
        <div style="background: var(--bg-light); padding: 1.5rem; border-radius: 8px; text-align: center;">
            <h4 style="color: var(--main-red); margin: 0 0 1rem 0;">⚡ Dynamic Loading Complete</h4>
            <p>The dynamic feedback loading system ensures that feedback forms are always generated using the most current template configuration. This provides maximum flexibility for administrators while maintaining data integrity and user experience consistency.</p>
        </div>
    </div>
</body>
</html>
