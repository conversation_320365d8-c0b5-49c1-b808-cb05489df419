<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Design Test - SnowNavi Student Feedback</title>
    <link rel="icon" type="image/png" href="assets/picture/snownavi_logo.png">
    <style>
        :root {
            --main-red: #E53512;
            --bg-light: #F9F4F3;
            --text-dark: #2F2F2F;
            --text-gray: #717171;
            --contrast-white: #FFFFFF;
            --accent-blue: #9ED4E7;
        }

        body { 
            font-family: 'Noto Sans SC', sans-serif; 
            margin: 2rem; 
            background: var(--bg-light);
            color: var(--text-dark);
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--contrast-white);
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 3px solid var(--main-red);
        }
        .brand-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        .brand-logo img {
            height: 40px;
            width: auto;
        }
        .brand-name {
            font-size: 1.8rem;
            font-weight: bold;
            color: var(--main-red);
        }
        h1 { color: var(--main-red); }
        h2 { color: var(--text-dark); border-bottom: 2px solid var(--main-red); padding-bottom: 0.5rem; }
        .test-section { 
            margin: 2rem 0; 
            padding: 1.5rem; 
            border: 1px solid rgba(229, 53, 18, 0.1);
            border-radius: 8px;
            background: var(--bg-light);
        }
        .feature-card {
            background: var(--contrast-white);
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid var(--main-red);
            margin: 1rem 0;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }
        .feature-card h4 {
            margin: 0 0 0.5rem 0;
            color: var(--main-red);
        }
        .highlight {
            background: var(--contrast-white);
            padding: 1rem;
            border-radius: 6px;
            border: 2px solid var(--accent-blue);
            margin: 1rem 0;
            box-shadow: 0 2px 8px rgba(158, 212, 231, 0.2);
        }
        .portal-link {
            background: var(--contrast-white);
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
            margin: 2rem 0;
            border: 2px solid var(--main-red);
            box-shadow: 0 2px 8px rgba(229, 53, 18, 0.1);
        }
        .portal-link a {
            color: var(--contrast-white);
            text-decoration: none;
            font-weight: bold;
            font-size: 1.2rem;
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: var(--main-red);
            border-radius: 6px;
            transition: background-color 0.2s;
        }
        .portal-link a:hover {
            background: #c42e0f;
        }
        .contact-section {
            background: var(--bg-light);
            padding: 2rem;
            border-radius: 8px;
            text-align: center;
            border-top: 3px solid var(--main-red);
        }
        .contact-item {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin: 0.5rem 1rem;
            font-size: 1rem;
        }
        .contact-item a {
            color: var(--main-red);
            text-decoration: none;
            font-weight: 500;
        }
        .contact-item a:hover {
            text-decoration: underline;
        }
        .social-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 1rem;
        }
        .social-link {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.6rem 1rem;
            background: var(--contrast-white);
            border-radius: 6px;
            color: var(--text-dark);
            font-weight: 500;
            border: 1px solid rgba(229, 53, 18, 0.2);
            transition: transform 0.2s ease;
        }
        .social-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        .social-icon {
            width: 20px;
            height: 20px;
            object-fit: contain;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="brand-logo">
                <img src="assets/picture/snownavi_logo.png" alt="SnowNavi Logo">
                <span class="brand-name">SnowNavi</span>
            </div>
            <h1>🎨 New Design Preview</h1>
            <p>Updated student feedback portal with consistent branding</p>
        </div>
        
        <div class="highlight">
            <strong>Design Update:</strong> The student feedback portal now uses SnowNavi's official color scheme 
            and branding elements for a consistent user experience across the website.
        </div>
        
        <div class="portal-link">
            <p><strong>🎿 Updated Student Feedback Portal</strong></p>
            <a href="student_feedback.html" target="_blank">View New Design</a>
        </div>
        
        <div class="test-section">
            <h2>🎨 Design Changes</h2>
            
            <div class="feature-card">
                <h4>Brand Integration</h4>
                <p>Added SnowNavi logo and brand name to the header for consistent brand recognition</p>
            </div>
            
            <div class="feature-card">
                <h4>Color Scheme</h4>
                <p>Updated to use official SnowNavi colors: Main Red (#E53512), Light Background (#F9F4F3), and Accent Blue (#9ED4E7)</p>
            </div>
            
            <div class="feature-card">
                <h4>Typography</h4>
                <p>Switched to 'Noto Sans SC' font family to match the main website</p>
            </div>
            
            <div class="feature-card">
                <h4>Contact Information</h4>
                <p>Added footer section with email contact and social media links (WeChat & Xiaohongshu)</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📱 Test Instructions</h2>
            
            <ol>
                <li><strong>Visual Consistency:</strong> Compare the new design with the main website (index.html)</li>
                <li><strong>Brand Recognition:</strong> Verify SnowNavi logo and branding are prominently displayed</li>
                <li><strong>Color Harmony:</strong> Check that all colors match the website's color scheme</li>
                <li><strong>Contact Access:</strong> Test the footer contact information and social media links</li>
                <li><strong>Mobile Experience:</strong> Test responsive design on different screen sizes</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h2>🔗 Quick Links</h2>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <div class="feature-card">
                    <h4>Main Website</h4>
                    <a href="index.html" target="_blank" style="color: var(--main-red);">index.html</a>
                </div>
                <div class="feature-card">
                    <h4>Student Portal</h4>
                    <a href="student_feedback.html" target="_blank" style="color: var(--main-red);">student_feedback.html</a>
                </div>
                <div class="feature-card">
                    <h4>Demo Page</h4>
                    <a href="demo_student_feedback.html" target="_blank" style="color: var(--main-red);">demo_student_feedback.html</a>
                </div>
                <div class="feature-card">
                    <h4>Test Guide</h4>
                    <a href="test_student_feedback.html" target="_blank" style="color: var(--main-red);">test_student_feedback.html</a>
                </div>
            </div>
        </div>
        
        <div class="contact-section">
            <h3 style="color: var(--main-red); margin-bottom: 1rem;">Contact SnowNavi</h3>
            <div>
                <div class="contact-item">
                    <span>📧</span>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="contact-item">
                    <span>🌐</span>
                    <a href="https://snownavi.ski" target="_blank">snownavi.ski</a>
                </div>
            </div>
            
            <div class="social-links">
                <div class="social-link">
                    <img src="assets/picture/wechat_logo.png" alt="WeChat" class="social-icon">
                    <span>SnowNavi指雪针</span>
                </div>
                <div class="social-link">
                    <img src="assets/picture/xiaohongshu_logo.png" alt="Little Red Book" class="social-icon">
                    <span>SnowNavi指雪针</span>
                </div>
            </div>
            
            <p style="margin-top: 1.5rem; color: var(--text-gray); font-size: 0.9rem;">
                &copy; 2025 SnowNavi Snow Club. Professional snowboarding instruction since 2021.
            </p>
        </div>
    </div>
</body>
</html>
