<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Activity Admin Panel</title>

  <!-- Favicon -->
  <link rel="icon" type="image/png" sizes="32x32" href="assets/picture/snownavi_logo.png">
  <link rel="icon" type="image/png" sizes="16x16" href="assets/picture/snownavi_logo.png">
  <link rel="shortcut icon" href="assets/picture/snownavi_logo.png">

  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      background: #f7f7f7;
      height: 100vh;
      display: flex;
      flex-direction: column;
    }
    h1 {
      color: #E53512;
      padding: 1rem;
      margin: 0;
    }
    .lang-tabs {
      padding: 0 1rem;
      margin-bottom: 1rem;
    }
    .lang-tabs button {
      margin-right: 1rem;
      padding: 0.5rem 1rem;
      background: #eee;
      border: 1px solid #ccc;
      cursor: pointer;
    }
    .lang-tabs button.active {
      background: #E53512;
      color: white;
    }
    .main-container {
      display: flex;
      flex: 1;
      overflow: hidden;
    }
    .activity-list {
      width: 300px;
      background: #fff;
      border-right: 1px solid #ddd;
      overflow-y: auto;
      padding: 1rem;
    }
    .activity-item {
      padding: 1rem;
      cursor: pointer;
      border-radius: 4px;
      margin-bottom: 0.5rem;
      border: 1px solid #eee;
      background: #f9f9f9;
      position: relative;
    }
    .activity-item:hover {
      background: #f0f0f0;
    }
    .activity-item.active {
      background: #E53512;
      color: white;
    }
    .activity-item-actions {
      position: absolute;
      top: 0.5rem;
      right: 0.5rem;
      opacity: 0;
      transition: opacity 0.2s;
    }
    .activity-item:hover .activity-item-actions {
      opacity: 1;
    }
    .activity-item.active .activity-item-actions {
      opacity: 1;
    }
    .duplicate-btn {
      background: #28a745;
      color: white;
      border: none;
      padding: 0.25rem 0.5rem;
      border-radius: 3px;
      cursor: pointer;
      font-size: 0.7rem;
      margin-left: 0.25rem;
    }
    .duplicate-btn:hover {
      background: #218838;
    }
    .activity-item h3 {
      margin: 0 0 0.5rem 0;
      font-size: 1rem;
    }
    .activity-item .activity-meta {
      font-size: 0.8rem;
      opacity: 0.8;
    }
    .form-container {
      flex: 1;
      padding: 1rem;
      overflow-y: auto;
    }
    .activity-form {
      background: white;
      padding: 1.5rem;
      border-radius: 8px;
      box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
    }
    label {
      font-weight: bold;
      display: block;
      margin-top: 1rem;
    }
    input, textarea, select {
      width: 100%;
      padding: 0.5rem;
      margin-top: 0.2rem;
      border: 1px solid #ccc;
      border-radius: 4px;
      box-sizing: border-box;
    }
    textarea {
      min-height: 80px;
      resize: vertical;
    }
    .form-row {
      display: flex;
      gap: 1rem;
      margin-top: 1rem;
    }
    .form-row > div {
      flex: 1;
    }
    .btn {
      background: #E53512;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      border-radius: 5px;
      cursor: pointer;
      margin: 0.5rem 0.5rem 0.5rem 0;
    }
    .btn-secondary {
      background: #666;
    }
    .btn-danger {
      background: #dc3545;
    }
    .actions {
      padding: 1rem;
      background: #fff;
      border-top: 1px solid #ddd;
      display: flex;
      align-items: center;
      gap: 1rem;
      flex-wrap: wrap;
    }
    .no-selection {
      display: flex;
      height: 100%;
      align-items: center;
      justify-content: center;
      color: #777;
      font-size: 1.2rem;
    }
    /* Authentication styles */
    .auth-container {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.9);
      z-index: 1000;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }
    .auth-message {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      text-align: center;
      max-width: 400px;
    }
    .auth-message h2 {
      color: #E53512;
      margin-top: 0;
    }
    .auth-btn {
      background: #E53512;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      border-radius: 5px;
      cursor: pointer;
      margin-top: 1rem;
      text-decoration: none;
      display: inline-block;
    }
    .user-info {
      display: flex;
      align-items: center;
      position: absolute;
      top: 1rem;
      right: 1rem;
    }
    .user-info img {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      margin-right: 0.5rem;
    }
    .logout-btn {
      background: none;
      border: none;
      color: #E53512;
      cursor: pointer;
      margin-left: 1rem;
      text-decoration: underline;
    }
    .status-badge {
      padding: 0.2rem 0.5rem;
      border-radius: 3px;
      font-size: 0.8rem;
      font-weight: bold;
    }
    .status-active {
      background: #28a745;
      color: white;
    }
    .status-inactive {
      background: #6c757d;
      color: white;
    }

    /* Mobile responsive styles */
    @media (max-width: 768px) {
      body {
        padding: 0;
      }

      h1 {
        font-size: 1.5rem;
        text-align: center;
        margin: 0.5rem;
        padding: 0 1rem;
      }

      .back-link {
        margin: 0.5rem 1rem !important;
        font-size: 0.9rem;
      }

      .lang-tabs {
        padding: 0 1rem;
        margin-bottom: 0.5rem;
      }

      .lang-tabs button {
        margin-right: 0.5rem;
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
      }

      .main-container {
        flex-direction: column;
        height: auto;
      }

      .activity-list {
        width: 100%;
        max-height: 40vh;
        border-right: none;
        border-bottom: 1px solid #ddd;
      }

      .activity-item {
        padding: 0.75rem;
      }

      .activity-item h3 {
        font-size: 0.9rem;
        margin-right: 2rem; /* Space for duplicate button */
      }

      .activity-item .activity-meta {
        font-size: 0.75rem;
      }

      .activity-item-actions {
        top: 0.25rem;
        right: 0.25rem;
        opacity: 1; /* Always visible on mobile */
      }

      .duplicate-btn {
        padding: 0.2rem 0.4rem;
        font-size: 0.6rem;
      }

      .form-container {
        padding: 0.5rem;
      }

      .activity-form {
        padding: 1rem;
      }

      .form-row {
        flex-direction: column;
        gap: 0.5rem;
      }

      .btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
        margin: 0.25rem 0.25rem 0.25rem 0;
      }

      .actions {
        padding: 0.75rem;
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
      }

      .actions > div {
        margin-left: 0 !important;
        text-align: center;
        font-size: 0.8rem;
      }

      .user-info {
        position: static;
        justify-content: center;
        margin: 0.5rem;
        padding: 0.5rem;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
      }

      .auth-message {
        margin: 1rem;
        padding: 1.5rem;
        max-width: calc(100% - 2rem);
      }

      .no-selection {
        font-size: 1rem;
        padding: 2rem 1rem;
        text-align: center;
      }
    }

    @media (max-width: 480px) {
      h1 {
        font-size: 1.3rem;
      }

      .lang-tabs button {
        padding: 0.3rem 0.6rem;
        font-size: 0.8rem;
        margin-right: 0.3rem;
      }

      .activity-list {
        max-height: 35vh;
      }

      .activity-item {
        padding: 0.5rem;
      }

      .activity-item h3 {
        font-size: 0.85rem;
      }

      .activity-form {
        padding: 0.75rem;
      }

      .btn {
        padding: 0.5rem 0.8rem;
        font-size: 0.85rem;
      }

      input, textarea, select {
        padding: 0.4rem;
        font-size: 0.9rem;
      }

      label {
        font-size: 0.9rem;
      }
    }
  </style>
</head>
<body>
  <!-- Authentication overlay -->
  <div class="auth-container" id="auth-container">
    <div class="auth-message">
      <h2>Authentication Required</h2>
      <p>You need to be logged in to access this page.</p>
      <a href="login.html" class="auth-btn">Go to Login</a>
    </div>
  </div>

  <div class="user-info" id="user-info"></div>

  <a href="admin.html" class="back-link" style="display: inline-block; margin: 1rem; color: #E53512; text-decoration: none;">← Back to Admin Panel</a>

  <h1>SnowNavi Activity Management</h1>
  <div class="lang-tabs">
    <button onclick="switchLang('en')" id="tab-en">English</button>
    <button onclick="switchLang('zh')" id="tab-zh">中文</button>
    <button onclick="switchLang('nl')" id="tab-nl">Nederlands</button>
  </div>

  <div class="main-container">
    <div class="activity-list" id="activity-list">
      <!-- Activities will be loaded here -->
    </div>
    <div class="form-container" id="form-container">
      <div class="no-selection">Select an activity from the left panel or create a new one</div>
    </div>
  </div>

  <div class="actions">
    <button class="btn" onclick="addNewActivity()">Add New Activity</button>
    <button class="btn btn-secondary" onclick="saveActivities()">Save Changes</button>
    <div style="margin-left: 1rem; color: #666; font-size: 0.9rem; display: flex; align-items: center;">
      💡 Tip: Click 📋 on any activity to duplicate it, or use the "Duplicate Activity" button when editing.
    </div>
  </div>

  <script>
    let currentLang = 'en';
    let activities = {};
    let courses = {};
    let selectedActivity = null;

    // Fetch configuration and check authentication
    async function checkAuth() {
      try {
        const response = await fetch('/api/config');
        if (!response.ok) {
          throw new Error('Failed to fetch configuration');
        }

        const config = await response.json();
        const authorizedEmail = config.authorizedEmail;

        const auth = JSON.parse(localStorage.getItem('snownavi_auth') || '{}');
        const authContainer = document.getElementById('auth-container');
        const userInfoContainer = document.getElementById('user-info');

        if (auth.email === authorizedEmail && auth.expiresAt && auth.expiresAt > Date.now()) {
          authContainer.style.display = 'none';
          userInfoContainer.innerHTML = `
            <img src="${auth.picture}" alt="Profile">
            <span>${auth.name}</span>
            <button class="logout-btn" onclick="logout()">Logout</button>
          `;
          loadData();
        } else {
          authContainer.style.display = 'flex';
          userInfoContainer.innerHTML = '';
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
        document.getElementById('auth-container').style.display = 'flex';
        document.querySelector('.auth-message p').textContent = 'Error loading configuration. Please try again later.';
      }
    }

    function logout() {
      localStorage.removeItem('snownavi_auth');
      window.location.href = 'login.html';
    }

    async function loadData() {
      try {
        // Load activities
        const activitiesRes = await fetch('data/activities.json');
        activities = await activitiesRes.json();

        // Load courses for type options
        const coursesRes = await fetch('data/courses.json');
        courses = await coursesRes.json();

        document.getElementById(`tab-${currentLang}`).classList.add('active');
        renderActivityList();
      } catch (error) {
        console.error('Error loading data:', error);
        alert('Failed to load data. Please try again later.');
      }
    }

    function switchLang(lang) {
      currentLang = lang;
      document.querySelectorAll('.lang-tabs button').forEach(btn => btn.classList.remove('active'));
      document.getElementById(`tab-${lang}`).classList.add('active');
      renderActivityList();
      if (selectedActivity) {
        renderActivityForm(selectedActivity);
      }
    }

    function renderActivityList() {
      const activityList = document.getElementById('activity-list');
      activityList.innerHTML = '';

      Object.keys(activities).forEach(activityId => {
        const activity = activities[activityId];
        const activityItem = document.createElement('div');
        activityItem.className = 'activity-item';

        const activityName = activity.name[currentLang] || activity.name.en || 'Unnamed Activity';
        const activityDate = new Date(activity.date).toLocaleDateString();
        const courseTitle = courses[activity.type] ? courses[activity.type][currentLang].title : activity.type;

        activityItem.innerHTML = `
          <h3>${activityName}</h3>
          <div class="activity-meta">
            <div>Type: ${courseTitle}</div>
            <div>Date: ${activityDate}</div>
            <span class="status-badge status-${activity.status}">${activity.status}</span>
          </div>
          <div class="activity-item-actions">
            <button class="duplicate-btn" onclick="quickDuplicateActivity('${activityId}', event)" title="Duplicate this activity">
              📋
            </button>
          </div>
        `;

        activityItem.dataset.activityId = activityId;
        activityItem.onclick = () => selectActivity(activityId);

        if (selectedActivity === activityId) {
          activityItem.classList.add('active');
        }

        activityList.appendChild(activityItem);
      });
    }

    function selectActivity(activityId) {
      selectedActivity = activityId;
      renderActivityList();
      renderActivityForm(activityId);
    }

    function renderActivityForm(activityId) {
      const activity = activities[activityId];
      const formContainer = document.getElementById('form-container');

      // Get course type options
      const courseOptions = Object.keys(courses).map(courseKey => {
        const courseTitle = courses[courseKey][currentLang].title;
        return `<option value="${courseKey}" ${activity.type === courseKey ? 'selected' : ''}>${courseTitle}</option>`;
      }).join('');

      formContainer.innerHTML = `
        <div class="activity-form">
          <h2>Edit Activity</h2>

          <label>Activity ID</label>
          <input type="text" id="activity-id" value="${activity.id}" readonly style="background: #f5f5f5;">

          <div class="form-row">
            <div>
              <label>Activity Name (English)</label>
              <input type="text" id="name-en" value="${activity.name.en || ''}" placeholder="Activity name in English">
            </div>
            <div>
              <label>Activity Name (中文)</label>
              <input type="text" id="name-zh" value="${activity.name.zh || ''}" placeholder="Activity name in Chinese">
            </div>
          </div>

          <div class="form-row">
            <div>
              <label>Activity Name (Nederlands)</label>
              <input type="text" id="name-nl" value="${activity.name.nl || ''}" placeholder="Activity name in Dutch">
            </div>
            <div>
              <label>Activity Type</label>
              <select id="activity-type">
                ${courseOptions}
              </select>
            </div>
          </div>

          <div class="form-row">
            <div>
              <label>Date</label>
              <input type="date" id="activity-date" value="${activity.date}">
            </div>
            <div>
              <label>Status</label>
              <select id="activity-status">
                <option value="active" ${activity.status === 'active' ? 'selected' : ''}>Active</option>
                <option value="inactive" ${activity.status === 'inactive' ? 'selected' : ''}>Inactive</option>
                <option value="completed" ${activity.status === 'completed' ? 'selected' : ''}>Completed</option>
                <option value="cancelled" ${activity.status === 'cancelled' ? 'selected' : ''}>Cancelled</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div>
              <label>Location (English)</label>
              <input type="text" id="location-en" value="${activity.location.en || ''}" placeholder="Location in English">
            </div>
            <div>
              <label>Location (中文)</label>
              <input type="text" id="location-zh" value="${activity.location.zh || ''}" placeholder="Location in Chinese">
            </div>
          </div>

          <label>Location (Nederlands)</label>
          <input type="text" id="location-nl" value="${activity.location.nl || ''}" placeholder="Location in Dutch">

          <label>Description (English)</label>
          <textarea id="description-en" placeholder="Description in English">${activity.description.en || ''}</textarea>

          <label>Description (中文)</label>
          <textarea id="description-zh" placeholder="Description in Chinese">${activity.description.zh || ''}</textarea>

          <label>Description (Nederlands)</label>
          <textarea id="description-nl" placeholder="Description in Dutch">${activity.description.nl || ''}</textarea>

          <div style="margin-top: 2rem;">
            <button class="btn" onclick="updateActivity()">Update Activity</button>
            <button class="btn btn-secondary" onclick="duplicateActivity()">Duplicate Activity</button>
            <button class="btn btn-danger" onclick="deleteActivity()">Delete Activity</button>
          </div>
        </div>
      `;
    }

    function updateActivity() {
      if (!selectedActivity) return;

      const activity = activities[selectedActivity];

      // Update activity data
      activity.name.en = document.getElementById('name-en').value;
      activity.name.zh = document.getElementById('name-zh').value;
      activity.name.nl = document.getElementById('name-nl').value;
      activity.type = document.getElementById('activity-type').value;
      activity.date = document.getElementById('activity-date').value;
      activity.status = document.getElementById('activity-status').value;
      activity.location.en = document.getElementById('location-en').value;
      activity.location.zh = document.getElementById('location-zh').value;
      activity.location.nl = document.getElementById('location-nl').value;
      activity.description.en = document.getElementById('description-en').value;
      activity.description.zh = document.getElementById('description-zh').value;
      activity.description.nl = document.getElementById('description-nl').value;
      activity.updatedAt = new Date().toISOString();

      renderActivityList();
      alert('Activity updated successfully! Remember to save changes.');
    }

    function quickDuplicateActivity(activityId, event) {
      // Prevent the click from selecting the activity
      event.stopPropagation();

      const originalActivity = activities[activityId];
      if (!originalActivity) return;

      const confirmMessage = `Duplicate "${originalActivity.name.en}"?\n\nThis will create a new activity with the same details but updated date and name.`;
      if (!confirm(confirmMessage)) {
        return;
      }

      performActivityDuplication(activityId);
    }

    function duplicateActivity() {
      if (!selectedActivity) return;

      const originalActivity = activities[selectedActivity];
      if (!originalActivity) return;

      const confirmMessage = `Duplicate "${originalActivity.name.en}"?\n\nThis will create a new activity with the same details but updated date and name.`;
      if (!confirm(confirmMessage)) {
        return;
      }

      performActivityDuplication(selectedActivity);
    }

    function performActivityDuplication(activityId) {
      const originalActivity = activities[activityId];
      if (!originalActivity) return;

      // Generate new activity ID
      const newActivityId = generateActivityId();

      // Create a deep copy of the original activity
      const duplicatedActivity = JSON.parse(JSON.stringify(originalActivity));

      // Update the duplicated activity with new information
      duplicatedActivity.id = newActivityId;

      // Update names to indicate it's a copy
      duplicatedActivity.name.en = `${originalActivity.name.en} (Copy)`;
      duplicatedActivity.name.zh = `${originalActivity.name.zh} (副本)`;
      duplicatedActivity.name.nl = `${originalActivity.name.nl} (Kopie)`;

      // Set date to today
      const today = new Date().toISOString().split('T')[0];
      duplicatedActivity.date = today;

      // Update timestamps
      duplicatedActivity.createdAt = new Date().toISOString();
      duplicatedActivity.updatedAt = new Date().toISOString();

      // Set status to active
      duplicatedActivity.status = 'active';

      // Add to activities
      activities[newActivityId] = duplicatedActivity;

      // Select the new activity and render
      selectedActivity = newActivityId;
      renderActivityList();
      renderActivityForm(newActivityId);

      alert(`Activity duplicated successfully!\n\nNew Activity ID: ${newActivityId}\n\nThe duplicated activity is now selected. Please review and update the details as needed, then save changes.`);
    }

    function deleteActivity() {
      if (!selectedActivity) return;

      if (confirm('Are you sure you want to delete this activity? This action cannot be undone.')) {
        delete activities[selectedActivity];
        selectedActivity = null;
        renderActivityList();
        document.getElementById('form-container').innerHTML = '<div class="no-selection">Select an activity from the left panel or create a new one</div>';
        alert('Activity deleted successfully! Remember to save changes.');
      }
    }

    function generateActivityId() {
      const currentYear = new Date().getFullYear();
      const prefix = `ACT${currentYear}`;

      let maxSequence = 0;
      Object.keys(activities).forEach(id => {
        if (id.startsWith(prefix)) {
          const sequenceStr = id.substring(prefix.length);
          const sequence = parseInt(sequenceStr, 10);
          if (!isNaN(sequence) && sequence > maxSequence) {
            maxSequence = sequence;
          }
        }
      });

      const newSequence = maxSequence + 1;
      return `${prefix}${newSequence.toString().padStart(4, '0')}`;
    }

    function addNewActivity() {
      const activityId = generateActivityId();
      const today = new Date().toISOString().split('T')[0];

      const newActivity = {
        id: activityId,
        name: {
          en: 'New Activity',
          zh: '新活动',
          nl: 'Nieuwe Activiteit'
        },
        type: Object.keys(courses)[0] || 'basi',
        date: today,
        location: {
          en: '',
          zh: '',
          nl: ''
        },
        description: {
          en: '',
          zh: '',
          nl: ''
        },
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      activities[activityId] = newActivity;
      selectedActivity = activityId;
      renderActivityList();
      renderActivityForm(activityId);
    }

    async function saveActivities() {
      try {
        const response = await fetch('data/activities.json', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(activities, null, 2)
        });

        if (response.ok) {
          alert('Activities saved successfully!');
        } else {
          alert('Error saving activities');
        }
      } catch (error) {
        console.error('Error saving activities:', error);
        alert('Failed to save activities. Please try again later.');
      }
    }

    // Initialize the page
    window.onload = checkAuth;
  </script>
</body>
</html>
