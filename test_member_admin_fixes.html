<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Member Admin Fixes</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        button { margin: 5px; padding: 10px; }
    </style>
</head>
<body>
    <h1>Member Admin Fixes Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Custom Fields Detection</h2>
        <button onclick="testCustomFields()">Test Custom Fields</button>
        <div id="custom-fields-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Member Data Loading</h2>
        <button onclick="testMemberLoading()">Test Member Loading</button>
        <div id="member-loading-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Save Members Function</h2>
        <button onclick="testSaveMembers()">Test Save Members</button>
        <div id="save-members-result"></div>
    </div>

    <script>
        let members = {};
        let customFields = ['gender']; // Default custom fields

        // Function to scan all members and update the custom fields list
        function updateCustomFieldsList() {
            const standardFields = ['id', 'name', 'isActive', 'validityPeriod', 'password'];
            const foundFields = new Set(['gender']); // Keep gender as default

            // Scan all members for custom fields
            Object.values(members).forEach(member => {
                Object.keys(member).forEach(field => {
                    if (!standardFields.includes(field)) {
                        foundFields.add(field);
                    }
                });
            });

            // Update the customFields array
            customFields = Array.from(foundFields);
        }

        async function testCustomFields() {
            const resultDiv = document.getElementById('custom-fields-result');
            try {
                // Load members data
                const res = await fetch('/data/members.json');
                members = await res.json();
                
                // Test the custom fields detection
                updateCustomFieldsList();
                
                resultDiv.innerHTML = `
                    <div class="success">✅ Custom fields detected: ${customFields.join(', ')}</div>
                    <div>Expected: gender, email</div>
                    <div>Found: ${customFields.includes('email') ? '✅' : '❌'} email field detected</div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        async function testMemberLoading() {
            const resultDiv = document.getElementById('member-loading-result');
            try {
                const res = await fetch('/data/members.json');
                members = await res.json();
                
                // Check if members with email field are loaded correctly
                const membersWithEmail = Object.values(members).filter(m => m.email);
                
                resultDiv.innerHTML = `
                    <div class="success">✅ Members loaded: ${Object.keys(members).length}</div>
                    <div>Members with email: ${membersWithEmail.length}</div>
                    <div>Sample member with email: ${membersWithEmail[0] ? membersWithEmail[0].name + ' (' + membersWithEmail[0].email + ')' : 'None'}</div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        async function testSaveMembers() {
            const resultDiv = document.getElementById('save-members-result');
            try {
                // Load current members
                const res = await fetch('/data/members.json');
                members = await res.json();
                
                // Test save function (without actually saving)
                const testSave = fetch('/data/members.json', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(members, null, 2)
                });
                
                resultDiv.innerHTML = `
                    <div class="success">✅ Save function test completed</div>
                    <div>Note: This is a dry run test</div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
