<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Feedback API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 2rem; }
        .test-section { margin: 2rem 0; padding: 1rem; border: 1px solid #ddd; }
        button { padding: 0.5rem 1rem; margin: 0.5rem; }
        .result { margin: 1rem 0; padding: 1rem; background: #f5f5f5; }
        .success { background: #d4edda; }
        .error { background: #f8d7da; }
    </style>
</head>
<body>
    <h1>Feedback API Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Save Feedbacks</h2>
        <button onclick="testSaveFeedbacks()">Test Save Feedbacks</button>
        <div id="save-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Load Feedbacks</h2>
        <button onclick="testLoadFeedbacks()">Test Load Feedbacks</button>
        <div id="load-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Set Test User</h2>
        <button onclick="setTestUser()">Set Test User Auth</button>
        <div id="auth-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Test 4: Create Feedback via API</h2>
        <button onclick="testCreateFeedback()">Test Create Feedback</button>
        <div id="create-result" class="result"></div>
    </div>

    <script>
        function setTestUser() {
            const resultDiv = document.getElementById('auth-result');
            try {
                // Create a test user auth object
                const testAuth = {
                    name: "Test Admin User",
                    email: "<EMAIL>",
                    picture: "https://via.placeholder.com/40",
                    expiresAt: Date.now() + (24 * 60 * 60 * 1000) // 24 hours from now
                };

                localStorage.setItem('snownavi_auth', JSON.stringify(testAuth));

                resultDiv.className = 'result success';
                resultDiv.innerHTML = `✅ Test user set successfully!<br><pre>${JSON.stringify(testAuth, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Failed to set test user: ${error.message}`;
            }
        }

        async function testSaveFeedbacks() {
            const resultDiv = document.getElementById('save-result');
            try {
                const testData = {
                    "CHK20250001": {
                        "id": "FB20250001",
                        "checkinId": "CHK20250001",
                        "activityId": "ACT20250001",
                        "memberId": "SN20250001",
                        "memberName": "Test User",
                        "feedback": "Great performance in today's lesson!",
                        "createdAt": new Date().toISOString(),
                        "updatedAt": new Date().toISOString(),
                        "createdBy": "Test Admin",
                        "createdByEmail": "<EMAIL>"
                    }
                };

                const response = await fetch('data/feedbacks.json', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData, null, 2)
                });

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ Save test successful!';
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Save test failed: ${error.message}`;
            }
        }

        async function testLoadFeedbacks() {
            const resultDiv = document.getElementById('load-result');
            try {
                const response = await fetch('data/feedbacks.json');
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Load test successful!<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Load test failed: ${error.message}`;
            }
        }

        async function testCreateFeedback() {
            const resultDiv = document.getElementById('create-result');
            try {
                // Get current user info (if set)
                const auth = JSON.parse(localStorage.getItem('snownavi_auth') || '{}');

                const feedbackData = {
                    checkinId: "CHK20250002",
                    activityId: "ACT20250001",
                    memberId: "SN20250002",
                    memberName: "Test User 2",
                    feedback: "Excellent improvement in technique!",
                    createdBy: auth.name || "Test User",
                    createdByEmail: auth.email || "<EMAIL>"
                };

                const response = await fetch('api/feedback', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(feedbackData)
                });

                if (response.ok) {
                    const result = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Create feedback test successful!<br><pre>${JSON.stringify(result, null, 2)}</pre>`;
                } else {
                    const errorData = await response.json();
                    throw new Error(`HTTP ${response.status}: ${errorData.error || response.statusText}`);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Create feedback test failed: ${error.message}`;
            }
        }
    </script>
</body>
</html>
