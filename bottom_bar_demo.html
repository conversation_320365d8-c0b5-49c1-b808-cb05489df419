<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bottom Contact Bar Demo - SnowNavi</title>
    <style>
        :root {
            --main-red: #E53512;
            --bg-light: #F9F4F3;
            --text-dark: #2F2F2F;
            --text-gray: #717171;
            --contrast-white: #FFFFFF;
            --accent-blue: #9ED4E7;
        }

        body { 
            font-family: 'Noto Sans SC', sans-serif; 
            margin: 2rem; 
            background: var(--bg-light);
            color: var(--text-dark);
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: var(--contrast-white);
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 3px solid var(--main-red);
        }
        h1 { color: var(--main-red); }
        .demo-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid var(--main-red);
            background: var(--bg-light);
        }
        .demo-section h3 {
            color: var(--main-red);
            margin: 0 0 1rem 0;
        }
        .bottom-bar-demo {
            background: rgba(249, 244, 243, 0.8);
            padding: 1rem 2rem;
            border-radius: 8px;
            margin: 2rem 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 40px;
            border: 2px solid var(--main-red);
        }
        .demo-logo {
            width: 60px;
            height: 60px;
            background: var(--main-red);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.5rem;
            flex-shrink: 0;
        }
        .demo-contact {
            flex: 1;
            min-width: 250px;
        }
        .demo-contact div {
            margin: 0.25rem 0;
        }
        .contact-email {
            font-size: 1rem;
            color: var(--text-gray);
        }
        .contact-website {
            font-size: 1rem;
            color: var(--text-gray);
        }
        .demo-qr {
            width: 60px;
            height: 60px;
            background: var(--text-dark);
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            flex-shrink: 0;
        }
        .layout-breakdown {
            background: var(--contrast-white);
            padding: 1.5rem;
            border-radius: 8px;
            margin: 1rem 0;
            border: 1px solid rgba(229, 53, 18, 0.2);
        }
        .layout-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem;
            margin: 0.5rem 0;
            background: var(--bg-light);
            border-radius: 4px;
            border-left: 3px solid var(--main-red);
        }
        .item-number {
            background: var(--main-red);
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
            flex-shrink: 0;
        }
        .improvement-list {
            list-style: none;
            padding: 0;
        }
        .improvement-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .improvement-icon {
            font-size: 1.2rem;
            width: 24px;
        }
        .btn {
            background: var(--main-red);
            color: var(--contrast-white);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 0.5rem;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #c42e0f;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(229, 53, 18, 0.3);
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin: 2rem 0;
        }
        .comparison-item {
            background: var(--contrast-white);
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid rgba(229, 53, 18, 0.2);
        }
        .comparison-item h4 {
            color: var(--main-red);
            margin: 0 0 1rem 0;
        }
        .before {
            background: #ffebee;
        }
        .after {
            background: #e8f5e8;
        }
        @media (max-width: 768px) {
            .bottom-bar-demo {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }
            .comparison-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📞 Bottom Contact Bar Design</h1>
            <p>Horizontal layout with all contact elements in one line</p>
        </div>
        
        <div class="demo-section">
            <h3>🎨 Bottom Bar Preview</h3>
            
            <div class="bottom-bar-demo">
                <div class="demo-logo">SN</div>
                <div class="demo-contact">
                    <div class="contact-email">Email: <EMAIL></div>
                    <div class="contact-website">Website: snownavi.ski</div>
                </div>
                <div class="demo-qr">微信</div>
                <div class="demo-qr">小红书</div>
            </div>
            
            <p style="text-align: center; color: var(--text-gray); font-style: italic;">
                All elements arranged horizontally for efficient space usage
            </p>
        </div>
        
        <div class="demo-section">
            <h3>📐 Layout Breakdown</h3>
            
            <div class="layout-breakdown">
                <div class="layout-item">
                    <div class="item-number">1</div>
                    <div>
                        <strong>SnowNavi Logo</strong><br>
                        <span style="color: var(--text-gray);">60px × 60px, leftmost position for brand recognition</span>
                    </div>
                </div>
                
                <div class="layout-item">
                    <div class="item-number">2</div>
                    <div>
                        <strong>Contact Information</strong><br>
                        <span style="color: var(--text-gray);">Email and website, center position, flexible width</span>
                    </div>
                </div>
                
                <div class="layout-item">
                    <div class="item-number">3</div>
                    <div>
                        <strong>WeChat QR Code</strong><br>
                        <span style="color: var(--text-gray);">60px × 60px, real wechat_qrcode.jpg image</span>
                    </div>
                </div>
                
                <div class="layout-item">
                    <div class="item-number">4</div>
                    <div>
                        <strong>Xiaohongshu QR Code</strong><br>
                        <span style="color: var(--text-gray);">60px × 60px, real xiaohongshu_qrcode.jpg image, rightmost</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>🚀 Design Improvements</h3>
            <ul class="improvement-list">
                <li><span class="improvement-icon">📏</span>Horizontal layout maximizes space efficiency</li>
                <li><span class="improvement-icon">🎯</span>Logical order: Brand → Contact → Social Media</li>
                <li><span class="improvement-icon">🔄</span>Eliminates duplicate SnowNavi branding</li>
                <li><span class="improvement-icon">📱</span>All contact methods accessible in one glance</li>
                <li><span class="improvement-icon">⚖️</span>Balanced visual weight across elements</li>
                <li><span class="improvement-icon">🎨</span>Consistent with modern design principles</li>
            </ul>
        </div>
        
        <div class="comparison-grid">
            <div class="comparison-item before">
                <h4>❌ Previous Layout</h4>
                <div style="text-align: center; line-height: 1.8;">
                    Email: <EMAIL><br>
                    Website: snownavi.ski<br><br>
                    Follow Us<br>
                    [WeChat QR] [Xiaohongshu QR]<br>
                    WeChat Official | Xiaohongshu<br>
                    SnowNavi指雪针 | SnowNavi指雪针
                </div>
                <p style="color: var(--text-gray); font-size: 0.9rem; margin-top: 1rem;">
                    Vertical layout, takes more space, redundant text labels
                </p>
            </div>
            
            <div class="comparison-item after">
                <h4>✅ New Layout</h4>
                <div style="display: flex; align-items: center; gap: 15px; justify-content: center;">
                    <div style="width: 40px; height: 40px; background: var(--main-red); border-radius: 4px; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.8rem;">SN</div>
                    <div style="text-align: left; font-size: 0.9rem;">
                        Email: <EMAIL><br>
                        Website: snownavi.ski
                    </div>
                    <div style="width: 30px; height: 30px; background: var(--text-dark); border-radius: 2px; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.6rem;">微信</div>
                    <div style="width: 30px; height: 30px; background: var(--text-dark); border-radius: 2px; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.6rem;">小红书</div>
                </div>
                <p style="color: var(--text-gray); font-size: 0.9rem; margin-top: 1rem;">
                    Horizontal layout, compact, clear hierarchy
                </p>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>🔧 Technical Implementation</h3>
            <div style="background: var(--contrast-white); padding: 1rem; border-radius: 6px; font-family: monospace; font-size: 0.9rem;">
<strong>Layout Calculation:</strong><br>
logoSize = 60px<br>
qrSize = 60px<br>
spacing = 40px<br>
contactTextWidth = 300px (approximate)<br><br>

<strong>Positioning:</strong><br>
totalWidth = logo + spacing + contact + spacing + qr1 + spacing + qr2<br>
startX = (canvasWidth - totalWidth) / 2<br>
logoX = startX<br>
contactX = startX + logoSize + spacing<br>
wechatX = contactX + contactTextWidth + spacing<br>
xhsX = wechatX + qrSize + spacing<br><br>

<strong>Background:</strong><br>
Light background bar (rgba(249, 244, 243, 0.8))<br>
Height: 80px, positioned at Y: 1150
            </div>
        </div>
        
        <div style="text-align: center; margin: 2rem 0;">
            <h3 style="color: var(--main-red); margin-bottom: 1rem;">🧪 Test the Bottom Bar</h3>
            <a href="certificate_preview.html" target="_blank" class="btn">🎨 Preview Certificate</a>
            <a href="student_feedback.html" target="_blank" class="btn">🎿 Student Portal</a>
            <a href="certificate_verification.html" target="_blank" class="btn">🔍 Layout Verification</a>
        </div>
        
        <div style="background: var(--bg-light); padding: 1.5rem; border-radius: 8px; text-align: center;">
            <h4 style="color: var(--main-red); margin: 0 0 1rem 0;">🎯 Horizontal Contact Bar Complete</h4>
            <p>The new bottom contact bar efficiently combines SnowNavi logo, contact information, and social media QR codes in a single horizontal line. This design eliminates redundancy, maximizes space usage, and provides a clean, professional appearance that matches the certificate's overall design aesthetic.</p>
        </div>
    </div>
</body>
</html>
