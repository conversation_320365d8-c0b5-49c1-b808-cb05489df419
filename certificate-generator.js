// Enhanced Certificate Generator for SnowNavi
// This file contains advanced certificate generation functions

class CertificateGenerator {
    constructor() {
        this.imageCache = new Map();
        this.qrCodeCache = new Map();
    }

    // Generate real member QR code using the same method as member.html
    async generateMemberQRCode(memberId, size) {
        // Create the member URL (same as member.html)
        const memberUrl = `${window.location.origin}/member.html?id=${memberId}`;

        // Use QR Server API (same as member.html)
        const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(memberUrl)}`;

        try {
            // Load the QR code image
            const qrImage = await this.loadImage(qrCodeUrl);
            return qrImage;
        } catch (error) {
            console.warn('Failed to load QR code from API, using fallback pattern');
            // Fallback to pattern if API fails
            return this.generateMemberQRPattern(memberId, size);
        }
    }

    // Fallback QR code pattern (used if <PERSON> fails)
    generateMemberQRPattern(memberId, size) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = size;
        canvas.height = size;

        // Create QR-like pattern based on member ID
        const moduleSize = size / 21; // Standard QR code is 21x21 modules for version 1
        ctx.fillStyle = '#000000';

        // Generate pattern based on member ID
        let hash = 0;
        for (let i = 0; i < memberId.length; i++) {
            hash = ((hash << 5) - hash + memberId.charCodeAt(i)) & 0xffffffff;
        }

        // Create finder patterns (corners)
        this.drawFinderPattern(ctx, 0, 0, moduleSize);
        this.drawFinderPattern(ctx, 14 * moduleSize, 0, moduleSize);
        this.drawFinderPattern(ctx, 0, 14 * moduleSize, moduleSize);

        // Fill data area with pattern
        for (let x = 0; x < 21; x++) {
            for (let y = 0; y < 21; y++) {
                // Skip finder patterns
                if ((x < 9 && y < 9) || (x > 12 && y < 9) || (x < 9 && y > 12)) continue;

                const value = (hash + x * 31 + y * 17) % 100;
                if (value < 50) {
                    ctx.fillRect(x * moduleSize, y * moduleSize, moduleSize, moduleSize);
                }
            }
        }

        return canvas;
    }

    // Draw QR code finder pattern
    drawFinderPattern(ctx, x, y, moduleSize) {
        // Outer 7x7 square
        ctx.fillRect(x, y, 7 * moduleSize, 7 * moduleSize);
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(x + moduleSize, y + moduleSize, 5 * moduleSize, 5 * moduleSize);
        ctx.fillStyle = '#000000';
        ctx.fillRect(x + 2 * moduleSize, y + 2 * moduleSize, 3 * moduleSize, 3 * moduleSize);
    }

    // Load and cache images
    async loadImage(src) {
        if (this.imageCache.has(src)) {
            return this.imageCache.get(src);
        }

        return new Promise((resolve, reject) => {
            const img = new Image();
            img.crossOrigin = 'anonymous';
            img.onload = () => {
                this.imageCache.set(src, img);
                resolve(img);
            };
            img.onerror = (error) => {
                console.warn(`Failed to load image: ${src}`, error);
                resolve(null); // Return null instead of rejecting
            };
            img.src = src;
        });
    }

    // Preload all required images
    async preloadImages() {
        const imageSources = [
            'assets/picture/snownavi_logo.png',
            'assets/picture/wechat_qrcode.jpg',
            'assets/picture/xiaohongshu_qrcode.jpg'
        ];

        const loadPromises = imageSources.map(src => this.loadImage(src));
        await Promise.all(loadPromises);
    }

    // Draw rounded rectangle
    drawRoundedRect(ctx, x, y, width, height, radius) {
        ctx.beginPath();
        ctx.moveTo(x + radius, y);
        ctx.lineTo(x + width - radius, y);
        ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        ctx.lineTo(x + width, y + height - radius);
        ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        ctx.lineTo(x + radius, y + height);
        ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        ctx.lineTo(x, y + radius);
        ctx.quadraticCurveTo(x, y, x + radius, y);
        ctx.closePath();
    }

    // Generate skill progress visualization
    drawSkillProgress(ctx, x, y, width, height, skillData) {
        const sections = [
            { name: 'Basic', total: 2, color: '#FF6B6B' },
            { name: 'Sliding', total: 5, color: '#4ECDC4' },
            { name: 'Control', total: 9, color: '#45B7D1' },
            { name: 'Turning', total: 5, color: '#96CEB4' },
            { name: 'Flow', total: 4, color: '#FFEAA7' }
        ];

        const completedSkills = skillData?.completedSkills || [];
        const sectionWidth = width / sections.length;
        
        sections.forEach((section, index) => {
            const sectionX = x + index * sectionWidth;
            const completed = this.countCompletedInSection(completedSkills, section.name.toLowerCase());
            const progress = completed / section.total;
            
            // Background bar
            ctx.fillStyle = '#E0E0E0';
            this.drawRoundedRect(ctx, sectionX + 10, y, sectionWidth - 20, height, 5);
            ctx.fill();
            
            // Progress bar
            ctx.fillStyle = section.color;
            this.drawRoundedRect(ctx, sectionX + 10, y, (sectionWidth - 20) * progress, height, 5);
            ctx.fill();
            
            // Section label
            ctx.fillStyle = '#2F2F2F';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(section.name, sectionX + sectionWidth / 2, y + height + 20);
            ctx.fillText(`${completed}/${section.total}`, sectionX + sectionWidth / 2, y + height + 40);
        });
    }

    countCompletedInSection(completedSkills, sectionName) {
        const sectionSkills = {
            basic: ['equipment-intro', 'single-foot-familiarity'],
            sliding: ['single-foot-sliding', 'single-foot-climbing', 'single-foot-straight', 'single-foot-heel-brake', 'single-foot-j-turn'],
            control: ['static-gas-pedal', 'single-heel-side-push', 'single-toe-side-push', 'both-heel-side-push', 'both-toe-side-push', 'both-heel-falling-leaf', 'both-toe-falling-leaf', 'both-heel-power-falling-leaf', 'both-toe-power-falling-leaf'],
            turning: ['static-rotation', 'step-turns', 'j-turns', 'walking-edge-change', 'beginner-turns'],
            flow: ['edge-change-traverse', 'traverse-body-movement', 'continuous-edge-change', 'scrub-360']
        };

        const skills = sectionSkills[sectionName] || [];
        return skills.filter(skill => completedSkills.includes(skill)).length;
    }

    // Main certificate generation function
    async generateAdvancedCertificate(data) {
        // Preload all images first
        await this.preloadImages();

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Set canvas size (3:4 ratio, high resolution)
        const width = 1200;
        const height = 1600;
        canvas.width = width;
        canvas.height = height;

        // Background gradient
        const gradient = ctx.createLinearGradient(0, 0, 0, height);
        gradient.addColorStop(0, '#F9F4F3');
        gradient.addColorStop(0.5, '#FFFFFF');
        gradient.addColorStop(1, '#F9F4F3');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, width, height);
        
        // Decorative border
        ctx.strokeStyle = '#E53512';
        ctx.lineWidth = 12;
        this.drawRoundedRect(ctx, 30, 30, width - 60, height - 60, 20);
        ctx.stroke();
        
        // Inner decorative border
        ctx.strokeStyle = '#9ED4E7';
        ctx.lineWidth = 6;
        this.drawRoundedRect(ctx, 50, 50, width - 100, height - 100, 15);
        ctx.stroke();
        
        // Header section
        ctx.fillStyle = '#E53512';
        ctx.font = 'bold 64px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('CERTIFICATE', width / 2, 160);

        ctx.font = 'bold 36px Arial';
        ctx.fillText('OF SNOWBOARDING ACHIEVEMENT', width / 2, 210);

        // SnowNavi logo and branding
        const logo = await this.loadImage('assets/picture/snownavi_logo.png');
        if (logo) {
            const logoSize = 80;
            const logoX = width / 2 - logoSize / 2;
            const logoY = 240;
            ctx.drawImage(logo, logoX, logoY, logoSize, logoSize);
        }

        ctx.fillStyle = '#2F2F2F';
        ctx.font = 'bold 32px Arial';
        ctx.fillText('SnowNavi Snow Club', width / 2, 350);

        ctx.fillStyle = '#717171';
        ctx.font = '20px Arial';
        ctx.fillText('Professional Snowboarding Instruction Since 2021', width / 2, 380);
        
        // Student information section
        ctx.fillStyle = '#2F2F2F';
        ctx.font = '28px Arial';
        ctx.fillText('This certifies that', width / 2, 470);

        // Course information
        ctx.font = '28px Arial';
        ctx.fillText('has successfully participated in', width / 2, 520);

        ctx.fillStyle = '#E53512';
        ctx.font = 'bold 36px Arial';
        ctx.fillText(data.activityName, width / 2, 570);

        ctx.fillStyle = '#2F2F2F';
        ctx.font = '22px Arial';
        ctx.fillText(`Course Date: ${data.activityDate}`, width / 2, 610);
        
        // Skills progress section
        if (data.feedback && data.feedback.skillAssessment) {
            ctx.fillStyle = '#2F2F2F';
            ctx.font = 'bold 28px Arial';
            ctx.fillText('Snowboarding Skills Progress', width / 2, 700);

            this.drawSkillProgress(ctx, 150, 730, width - 300, 30, data.feedback.skillAssessment);

            const completedSkills = data.feedback.skillAssessment.completedSkills || [];
            const totalSkills = 25;
            const completionRate = Math.round((completedSkills.length / totalSkills) * 100);

            ctx.fillStyle = '#28a745';
            ctx.font = 'bold 32px Arial';
            ctx.fillText(`Overall Progress: ${completedSkills.length}/${totalSkills} Skills (${completionRate}%)`, width / 2, 850);
        }
        
        // Member Information Section (Name, ID, QR Code together)
        const memberSectionY = 920;

        // Section background
        ctx.fillStyle = 'rgba(249, 244, 243, 0.8)';
        this.drawRoundedRect(ctx, 100, memberSectionY - 20, width - 200, 200, 10);
        ctx.fill();

        // Section border
        ctx.strokeStyle = '#E53512';
        ctx.lineWidth = 2;
        this.drawRoundedRect(ctx, 100, memberSectionY - 20, width - 200, 200, 10);
        ctx.stroke();

        // Member section title
        ctx.fillStyle = '#E53512';
        ctx.font = 'bold 24px Arial';
        ctx.fillText('Member Information', width / 2, memberSectionY + 10);

        // Member name (prominent)
        ctx.fillStyle = '#E53512';
        ctx.font = 'bold 36px Arial';
        ctx.fillText(data.memberName, width / 2, memberSectionY + 50);

        // Member ID
        ctx.fillStyle = '#2F2F2F';
        ctx.font = '20px Arial';
        ctx.fillText(`Member ID: ${data.memberId}`, width / 2, memberSectionY + 80);

        // Member QR Code
        const memberQRSize = 80;
        const memberQRX = width / 2 - memberQRSize / 2;
        const memberQRY = memberSectionY + 100;

        try {
            const memberQR = await this.generateMemberQRCode(data.memberId, memberQRSize);
            if (memberQR) {
                ctx.drawImage(memberQR, memberQRX, memberQRY, memberQRSize, memberQRSize);
            } else {
                const fallbackQR = this.generateMemberQRPattern(data.memberId, memberQRSize);
                ctx.drawImage(fallbackQR, memberQRX, memberQRY, memberQRSize, memberQRSize);
            }
        } catch (error) {
            console.warn('Failed to generate member QR code, using fallback');
            const fallbackQR = this.generateMemberQRPattern(data.memberId, memberQRSize);
            ctx.drawImage(fallbackQR, memberQRX, memberQRY, memberQRSize, memberQRSize);
        }

        // QR Code label
        ctx.fillStyle = '#717171';
        ctx.font = '14px Arial';
        ctx.fillText('Scan to verify member', width / 2, memberQRY + memberQRSize + 20);
        
        // SnowNavi Club Information Section
        const clubSectionY = 1150;

        // Section background
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        this.drawRoundedRect(ctx, 80, clubSectionY - 20, width - 160, 280, 10);
        ctx.fill();

        // Section border
        ctx.strokeStyle = '#E53512';
        ctx.lineWidth = 3;
        this.drawRoundedRect(ctx, 80, clubSectionY - 20, width - 160, 280, 10);
        ctx.stroke();

        // SnowNavi logo (larger in club section)
        const clubLogo = await this.loadImage('assets/picture/snownavi_logo.png');
        if (clubLogo) {
            const clubLogoSize = 60;
            const clubLogoX = width / 2 - clubLogoSize / 2;
            const clubLogoY = clubSectionY;
            ctx.drawImage(clubLogo, clubLogoX, clubLogoY, clubLogoSize, clubLogoSize);
        }

        // Club name
        ctx.fillStyle = '#E53512';
        ctx.font = 'bold 28px Arial';
        ctx.fillText('SnowNavi Snow Club', width / 2, clubSectionY + 85);

        // Professional tagline
        ctx.fillStyle = '#2F2F2F';
        ctx.font = '18px Arial';
        ctx.fillText('Professional Snowboarding Instruction Since 2021', width / 2, clubSectionY + 110);

        // Contact information
        ctx.fillStyle = '#717171';
        ctx.font = '16px Arial';
        ctx.fillText('Email: <EMAIL>  |  Website: snownavi.ski', width / 2, clubSectionY + 140);

        // Social media section
        ctx.fillStyle = '#2F2F2F';
        ctx.font = 'bold 16px Arial';
        ctx.fillText('Follow Us', width / 2, clubSectionY + 170);

        // Social media QR codes
        const socialQRSize = 60;
        const socialY = clubSectionY + 185;

        // WeChat QR
        const wechatQR = await this.loadImage('assets/picture/wechat_qrcode.jpg');
        if (wechatQR) {
            ctx.drawImage(wechatQR, width / 2 - 80, socialY, socialQRSize, socialQRSize);
        } else {
            ctx.fillStyle = '#2F2F2F';
            ctx.fillRect(width / 2 - 80, socialY, socialQRSize, socialQRSize);
        }
        ctx.fillStyle = '#717171';
        ctx.font = '12px Arial';
        ctx.fillText('WeChat Official', width / 2 - 50, socialY + socialQRSize + 12);
        ctx.fillText('SnowNavi指雪针', width / 2 - 50, socialY + socialQRSize + 25);

        // Xiaohongshu QR
        const xhsQR = await this.loadImage('assets/picture/xiaohongshu_qrcode.jpg');
        if (xhsQR) {
            ctx.drawImage(xhsQR, width / 2 + 20, socialY, socialQRSize, socialQRSize);
        } else {
            ctx.fillStyle = '#2F2F2F';
            ctx.fillRect(width / 2 + 20, socialY, socialQRSize, socialQRSize);
        }
        ctx.fillStyle = '#717171';
        ctx.font = '12px Arial';
        ctx.fillText('Xiaohongshu', width / 2 + 50, socialY + socialQRSize + 12);
        ctx.fillText('SnowNavi指雪针', width / 2 + 50, socialY + socialQRSize + 25);
        
        // Footer
        ctx.fillStyle = '#717171';
        ctx.font = '14px Arial';
        const currentDate = new Date().toLocaleDateString();
        ctx.fillText(`Certificate issued on ${currentDate}`, width / 2, 1480);

        ctx.font = '12px Arial';
        ctx.fillText('This certificate acknowledges participation and skill development', width / 2, 1500);
        ctx.fillText('in SnowNavi\'s professional snowboarding instruction program', width / 2, 1515);

        ctx.fillText('© 2025 SnowNavi Snow Club. All rights reserved.', width / 2, 1540);
        
        // Decorative elements
        ctx.fillStyle = '#9ED4E7';

        // Corner decorations
        ctx.beginPath();
        ctx.arc(120, 120, 15, 0, 2 * Math.PI);
        ctx.fill();

        ctx.beginPath();
        ctx.arc(width - 120, 120, 15, 0, 2 * Math.PI);
        ctx.fill();

        ctx.beginPath();
        ctx.arc(120, height - 120, 15, 0, 2 * Math.PI);
        ctx.fill();

        ctx.beginPath();
        ctx.arc(width - 120, height - 120, 15, 0, 2 * Math.PI);
        ctx.fill();

        // Additional decorative elements around logo
        ctx.fillStyle = '#E53512';
        ctx.globalAlpha = 0.1;
        ctx.beginPath();
        ctx.arc(width / 2 - 150, 300, 30, 0, 2 * Math.PI);
        ctx.fill();

        ctx.beginPath();
        ctx.arc(width / 2 + 150, 300, 30, 0, 2 * Math.PI);
        ctx.fill();

        ctx.globalAlpha = 1.0;
        
        return canvas;
    }
}

// Export for use in main page
window.CertificateGenerator = CertificateGenerator;
