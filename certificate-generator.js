// Enhanced Certificate Generator for SnowNavi
// This file contains advanced certificate generation functions

class CertificateGenerator {
    constructor() {
        this.qrCodeCache = new Map();
    }

    // Generate QR code using a simple pattern (placeholder for real QR library)
    generateQRPattern(text, size) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = size;
        canvas.height = size;
        
        // Simple pattern generation (in real implementation, use QR library)
        const moduleSize = size / 25;
        ctx.fillStyle = '#000000';
        
        // Create a simple pattern based on text hash
        let hash = 0;
        for (let i = 0; i < text.length; i++) {
            hash = ((hash << 5) - hash + text.charCodeAt(i)) & 0xffffffff;
        }
        
        for (let x = 0; x < 25; x++) {
            for (let y = 0; y < 25; y++) {
                const value = (hash + x * 31 + y * 17) % 100;
                if (value < 45) {
                    ctx.fillRect(x * moduleSize, y * moduleSize, moduleSize, moduleSize);
                }
            }
        }
        
        return canvas;
    }

    // Load and cache images
    async loadImage(src) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.crossOrigin = 'anonymous';
            img.onload = () => resolve(img);
            img.onerror = reject;
            img.src = src;
        });
    }

    // Draw rounded rectangle
    drawRoundedRect(ctx, x, y, width, height, radius) {
        ctx.beginPath();
        ctx.moveTo(x + radius, y);
        ctx.lineTo(x + width - radius, y);
        ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        ctx.lineTo(x + width, y + height - radius);
        ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        ctx.lineTo(x + radius, y + height);
        ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        ctx.lineTo(x, y + radius);
        ctx.quadraticCurveTo(x, y, x + radius, y);
        ctx.closePath();
    }

    // Generate skill progress visualization
    drawSkillProgress(ctx, x, y, width, height, skillData) {
        const sections = [
            { name: 'Basic', total: 2, color: '#FF6B6B' },
            { name: 'Sliding', total: 5, color: '#4ECDC4' },
            { name: 'Control', total: 9, color: '#45B7D1' },
            { name: 'Turning', total: 5, color: '#96CEB4' },
            { name: 'Flow', total: 4, color: '#FFEAA7' }
        ];

        const completedSkills = skillData?.completedSkills || [];
        const sectionWidth = width / sections.length;
        
        sections.forEach((section, index) => {
            const sectionX = x + index * sectionWidth;
            const completed = this.countCompletedInSection(completedSkills, section.name.toLowerCase());
            const progress = completed / section.total;
            
            // Background bar
            ctx.fillStyle = '#E0E0E0';
            this.drawRoundedRect(ctx, sectionX + 10, y, sectionWidth - 20, height, 5);
            ctx.fill();
            
            // Progress bar
            ctx.fillStyle = section.color;
            this.drawRoundedRect(ctx, sectionX + 10, y, (sectionWidth - 20) * progress, height, 5);
            ctx.fill();
            
            // Section label
            ctx.fillStyle = '#2F2F2F';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(section.name, sectionX + sectionWidth / 2, y + height + 20);
            ctx.fillText(`${completed}/${section.total}`, sectionX + sectionWidth / 2, y + height + 40);
        });
    }

    countCompletedInSection(completedSkills, sectionName) {
        const sectionSkills = {
            basic: ['equipment-intro', 'single-foot-familiarity'],
            sliding: ['single-foot-sliding', 'single-foot-climbing', 'single-foot-straight', 'single-foot-heel-brake', 'single-foot-j-turn'],
            control: ['static-gas-pedal', 'single-heel-side-push', 'single-toe-side-push', 'both-heel-side-push', 'both-toe-side-push', 'both-heel-falling-leaf', 'both-toe-falling-leaf', 'both-heel-power-falling-leaf', 'both-toe-power-falling-leaf'],
            turning: ['static-rotation', 'step-turns', 'j-turns', 'walking-edge-change', 'beginner-turns'],
            flow: ['edge-change-traverse', 'traverse-body-movement', 'continuous-edge-change', 'scrub-360']
        };

        const skills = sectionSkills[sectionName] || [];
        return skills.filter(skill => completedSkills.includes(skill)).length;
    }

    // Main certificate generation function
    async generateAdvancedCertificate(data) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        // Set canvas size (3:4 ratio, high resolution)
        const width = 1200;
        const height = 1600;
        canvas.width = width;
        canvas.height = height;
        
        // Background gradient
        const gradient = ctx.createLinearGradient(0, 0, 0, height);
        gradient.addColorStop(0, '#F9F4F3');
        gradient.addColorStop(0.5, '#FFFFFF');
        gradient.addColorStop(1, '#F9F4F3');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, width, height);
        
        // Decorative border
        ctx.strokeStyle = '#E53512';
        ctx.lineWidth = 12;
        this.drawRoundedRect(ctx, 30, 30, width - 60, height - 60, 20);
        ctx.stroke();
        
        // Inner decorative border
        ctx.strokeStyle = '#9ED4E7';
        ctx.lineWidth = 6;
        this.drawRoundedRect(ctx, 50, 50, width - 100, height - 100, 15);
        ctx.stroke();
        
        // Header section
        ctx.fillStyle = '#E53512';
        ctx.font = 'bold 64px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('CERTIFICATE', width / 2, 160);
        
        ctx.font = 'bold 36px Arial';
        ctx.fillText('OF SNOWBOARDING ACHIEVEMENT', width / 2, 210);
        
        // SnowNavi branding
        ctx.fillStyle = '#2F2F2F';
        ctx.font = 'bold 32px Arial';
        ctx.fillText('SnowNavi Snow Club', width / 2, 280);
        
        ctx.fillStyle = '#717171';
        ctx.font = '20px Arial';
        ctx.fillText('Professional Snowboarding Instruction Since 2021', width / 2, 310);
        
        // Student information section
        ctx.fillStyle = '#2F2F2F';
        ctx.font = '28px Arial';
        ctx.fillText('This certifies that', width / 2, 400);
        
        ctx.fillStyle = '#E53512';
        ctx.font = 'bold 48px Arial';
        ctx.fillText(data.memberName, width / 2, 460);
        
        ctx.fillStyle = '#2F2F2F';
        ctx.font = '24px Arial';
        ctx.fillText(`Member ID: ${data.memberId}`, width / 2, 500);
        
        // Course information
        ctx.font = '28px Arial';
        ctx.fillText('has successfully participated in', width / 2, 580);
        
        ctx.fillStyle = '#E53512';
        ctx.font = 'bold 36px Arial';
        ctx.fillText(data.activityName, width / 2, 630);
        
        ctx.fillStyle = '#2F2F2F';
        ctx.font = '22px Arial';
        ctx.fillText(`Course Date: ${data.activityDate}`, width / 2, 670);
        
        // Skills progress section
        if (data.feedback && data.feedback.skillAssessment) {
            ctx.fillStyle = '#2F2F2F';
            ctx.font = 'bold 28px Arial';
            ctx.fillText('Snowboarding Skills Progress', width / 2, 750);
            
            this.drawSkillProgress(ctx, 150, 780, width - 300, 30, data.feedback.skillAssessment);
            
            const completedSkills = data.feedback.skillAssessment.completedSkills || [];
            const totalSkills = 25;
            const completionRate = Math.round((completedSkills.length / totalSkills) * 100);
            
            ctx.fillStyle = '#28a745';
            ctx.font = 'bold 32px Arial';
            ctx.fillText(`Overall Progress: ${completedSkills.length}/${totalSkills} Skills (${completionRate}%)`, width / 2, 900);
        }
        
        // QR Codes section
        const memberUrl = `${window.location.origin}/member.html?id=${data.memberId}`;
        const memberQR = this.generateQRPattern(memberUrl, 100);
        ctx.drawImage(memberQR, width / 2 - 50, 950, 100, 100);
        
        ctx.fillStyle = '#2F2F2F';
        ctx.font = '16px Arial';
        ctx.fillText('Scan to verify member', width / 2, 1070);
        
        // Contact information
        ctx.fillStyle = '#717171';
        ctx.font = '20px Arial';
        ctx.fillText('Contact Information', width / 2, 1140);
        
        ctx.font = '18px Arial';
        ctx.fillText('Email: <EMAIL>', width / 2, 1170);
        ctx.fillText('Website: snownavi.ski', width / 2, 1195);
        
        // Social media QR codes
        const socialQRSize = 70;
        const socialY = 1230;
        
        // WeChat QR
        const wechatQR = this.generateQRPattern('SnowNavi指雪针 WeChat', socialQRSize);
        ctx.drawImage(wechatQR, width / 2 - 100, socialY, socialQRSize, socialQRSize);
        ctx.fillStyle = '#717171';
        ctx.font = '14px Arial';
        ctx.fillText('WeChat', width / 2 - 65, socialY + socialQRSize + 15);
        
        // Xiaohongshu QR
        const xhsQR = this.generateQRPattern('SnowNavi指雪针 Xiaohongshu', socialQRSize);
        ctx.drawImage(xhsQR, width / 2 + 30, socialY, socialQRSize, socialQRSize);
        ctx.fillText('Xiaohongshu', width / 2 + 65, socialY + socialQRSize + 15);
        
        // Footer
        ctx.fillStyle = '#717171';
        ctx.font = '16px Arial';
        const currentDate = new Date().toLocaleDateString();
        ctx.fillText(`Certificate issued on ${currentDate}`, width / 2, 1380);
        
        ctx.font = '14px Arial';
        ctx.fillText('This certificate acknowledges participation and skill development', width / 2, 1420);
        ctx.fillText('in SnowNavi\'s professional snowboarding instruction program', width / 2, 1440);
        
        // Decorative elements
        ctx.fillStyle = '#9ED4E7';
        ctx.beginPath();
        ctx.arc(150, 150, 20, 0, 2 * Math.PI);
        ctx.fill();
        
        ctx.beginPath();
        ctx.arc(width - 150, 150, 20, 0, 2 * Math.PI);
        ctx.fill();
        
        ctx.beginPath();
        ctx.arc(150, height - 150, 20, 0, 2 * Math.PI);
        ctx.fill();
        
        ctx.beginPath();
        ctx.arc(width - 150, height - 150, 20, 0, 2 * Math.PI);
        ctx.fill();
        
        return canvas;
    }
}

// Export for use in main page
window.CertificateGenerator = CertificateGenerator;
