// Enhanced Certificate Generator for SnowNavi
// This file contains advanced certificate generation functions

class CertificateGenerator {
    constructor() {
        this.imageCache = new Map();
        this.qrCodeCache = new Map();

        // Multi-language text configurations
        this.translations = {
            en: {
                companyName: 'SnowNavi Snow Club',
                tagline: 'Professional Snowboarding Instruction Since 2021',
                certificateTitle: 'CERTIFICATE',
                certificateSubtitle: 'OF SNOWBOARDING ACHIEVEMENT',
                certifiesText: 'This certifies that',
                memberIdText: 'Member ID:',
                participatedText: 'has successfully participated in',
                courseDateText: 'Course Date:',
                skillsProgressText: 'Snowboarding Skills Progress',
                overallProgressText: 'Overall Progress:',
                skillsText: 'Skills',
                memberVerificationText: 'Member Verification',
                scanToVerifyText: 'Scan to verify member',
                followUsText: 'Follow Us',
                wechatText: 'WeChat Official',
                xiaohongshuText: 'Xiaohongshu',
                socialAccountText: 'SnowNavi指雪针',
                contactText: 'Email: <EMAIL>  |  Website: snownavi.ski',
                issuedText: 'Certificate issued on',
                acknowledgmentText1: 'This certificate acknowledges participation and skill development',
                acknowledgmentText2: 'in SnowNavi\'s professional snowboarding instruction program',
                copyrightText: '© 2025 SnowNavi Snow Club. All rights reserved.',
                sectionNames: {
                    basic: 'Basic',
                    sliding: 'Sliding',
                    control: 'Control',
                    turning: 'Turning',
                    flow: 'Flow'
                }
            },
            zh: {
                companyName: 'SnowNavi 滑雪俱乐部',
                tagline: '专业单板滑雪教学 始于2021年',
                certificateTitle: '证书',
                certificateSubtitle: '单板滑雪成就',
                certifiesText: '特此证明',
                memberIdText: '会员编号：',
                participatedText: '已成功参与',
                courseDateText: '课程日期：',
                skillsProgressText: '单板滑雪技能进度',
                overallProgressText: '总体进度：',
                skillsText: '项技能',
                memberVerificationText: '会员验证',
                scanToVerifyText: '扫码验证会员身份',
                followUsText: '关注我们',
                wechatText: '微信公众号',
                xiaohongshuText: '小红书',
                socialAccountText: 'SnowNavi指雪针',
                contactText: '邮箱：<EMAIL>  |  网站：snownavi.ski',
                issuedText: '证书颁发日期',
                acknowledgmentText1: '本证书确认学员参与并在技能发展方面取得进步',
                acknowledgmentText2: '通过SnowNavi专业单板滑雪教学项目',
                copyrightText: '© 2025 SnowNavi滑雪俱乐部 版权所有',
                sectionNames: {
                    basic: '基础',
                    sliding: '滑动',
                    control: '控制',
                    turning: '转弯',
                    flow: '流畅转弯'
                }
            }
        };
    }

    // Generate real member QR code using the same method as member.html
    async generateMemberQRCode(memberId, size) {
        // Create the member URL (same as member.html)
        const memberUrl = `${window.location.origin}/member.html?id=${memberId}`;

        // Use QR Server API (same as member.html)
        const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(memberUrl)}`;

        try {
            // Load the QR code image
            const qrImage = await this.loadImage(qrCodeUrl);
            return qrImage;
        } catch (error) {
            console.warn('Failed to load QR code from API, using fallback pattern');
            // Fallback to pattern if API fails
            return this.generateMemberQRPattern(memberId, size);
        }
    }

    // Fallback QR code pattern (used if API fails)
    generateMemberQRPattern(memberId, size) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = size;
        canvas.height = size;

        // Create QR-like pattern based on member ID
        const moduleSize = size / 21; // Standard QR code is 21x21 modules for version 1
        ctx.fillStyle = '#000000';

        // Generate pattern based on member ID
        let hash = 0;
        for (let i = 0; i < memberId.length; i++) {
            hash = ((hash << 5) - hash + memberId.charCodeAt(i)) & 0xffffffff;
        }

        // Create finder patterns (corners)
        this.drawFinderPattern(ctx, 0, 0, moduleSize);
        this.drawFinderPattern(ctx, 14 * moduleSize, 0, moduleSize);
        this.drawFinderPattern(ctx, 0, 14 * moduleSize, moduleSize);

        // Fill data area with pattern
        for (let x = 0; x < 21; x++) {
            for (let y = 0; y < 21; y++) {
                // Skip finder patterns
                if ((x < 9 && y < 9) || (x > 12 && y < 9) || (x < 9 && y > 12)) continue;

                const value = (hash + x * 31 + y * 17) % 100;
                if (value < 50) {
                    ctx.fillRect(x * moduleSize, y * moduleSize, moduleSize, moduleSize);
                }
            }
        }

        return canvas;
    }

    // Draw QR code finder pattern
    drawFinderPattern(ctx, x, y, moduleSize) {
        // Outer 7x7 square
        ctx.fillRect(x, y, 7 * moduleSize, 7 * moduleSize);
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(x + moduleSize, y + moduleSize, 5 * moduleSize, 5 * moduleSize);
        ctx.fillStyle = '#000000';
        ctx.fillRect(x + 2 * moduleSize, y + 2 * moduleSize, 3 * moduleSize, 3 * moduleSize);
    }

    // Load and cache images
    async loadImage(src) {
        if (this.imageCache.has(src)) {
            return this.imageCache.get(src);
        }

        return new Promise((resolve) => {
            const img = new Image();
            img.crossOrigin = 'anonymous';
            img.onload = () => {
                this.imageCache.set(src, img);
                resolve(img);
            };
            img.onerror = (error) => {
                console.warn(`Failed to load image: ${src}`, error);
                resolve(null); // Return null instead of rejecting
            };
            img.src = src;
        });
    }

    // Preload all required images
    async preloadImages() {
        const imageSources = [
            'assets/picture/snownavi_logo.png',
            'assets/picture/wechat_qrcode.jpg',
            'assets/picture/xiaohongshu_qrcode.jpg',
            'assets/picture/certificate.png'
        ];

        const loadPromises = imageSources.map(src => this.loadImage(src));
        await Promise.all(loadPromises);
    }

    // Draw rounded rectangle
    drawRoundedRect(ctx, x, y, width, height, radius) {
        ctx.beginPath();
        ctx.moveTo(x + radius, y);
        ctx.lineTo(x + width - radius, y);
        ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        ctx.lineTo(x + width, y + height - radius);
        ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        ctx.lineTo(x + radius, y + height);
        ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        ctx.lineTo(x, y + radius);
        ctx.quadraticCurveTo(x, y, x + radius, y);
        ctx.closePath();
    }

    // Generate skill progress visualization
    drawSkillProgress(ctx, x, y, width, height, skillData, language = 'en') {
        const texts = this.translations[language] || this.translations.en;
        const sections = [
            { name: 'basic', total: 2, color: '#FF6B6B' },
            { name: 'sliding', total: 5, color: '#4ECDC4' },
            { name: 'control', total: 9, color: '#45B7D1' },
            { name: 'turning', total: 5, color: '#96CEB4' },
            { name: 'flow', total: 4, color: '#FFEAA7' }
        ];

        const completedSkills = skillData?.completedSkills || [];
        const sectionWidth = width / sections.length;

        sections.forEach((section, index) => {
            const sectionX = x + index * sectionWidth;
            const completed = this.countCompletedInSection(completedSkills, section.name);
            const progress = completed / section.total;

            // Background bar
            ctx.fillStyle = '#E0E0E0';
            this.drawRoundedRect(ctx, sectionX + 10, y, sectionWidth - 20, height, 5);
            ctx.fill();

            // Progress bar
            ctx.fillStyle = section.color;
            this.drawRoundedRect(ctx, sectionX + 10, y, (sectionWidth - 20) * progress, height, 5);
            ctx.fill();

            // Section label
            ctx.fillStyle = '#2F2F2F';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            const sectionName = texts.sectionNames[section.name] || section.name;
            ctx.fillText(sectionName, sectionX + sectionWidth / 2, y + height + 20);
            ctx.fillText(`${completed}/${section.total}`, sectionX + sectionWidth / 2, y + height + 40);
        });
    }

    countCompletedInSection(completedSkills, sectionName) {
        const sectionSkills = {
            basic: ['equipment-intro', 'single-foot-familiarity'],
            sliding: ['single-foot-sliding', 'single-foot-climbing', 'single-foot-straight', 'single-foot-heel-brake', 'single-foot-j-turn'],
            control: ['static-gas-pedal', 'single-heel-side-push', 'single-toe-side-push', 'both-heel-side-push', 'both-toe-side-push', 'both-heel-falling-leaf', 'both-toe-falling-leaf', 'both-heel-power-falling-leaf', 'both-toe-power-falling-leaf'],
            turning: ['static-rotation', 'step-turns', 'j-turns', 'walking-edge-change', 'beginner-turns'],
            flow: ['edge-change-traverse', 'traverse-body-movement', 'continuous-edge-change', 'scrub-360']
        };

        const skills = sectionSkills[sectionName] || [];
        return skills.filter(skill => completedSkills.includes(skill)).length;
    }

    // Main certificate generation function
    async generateAdvancedCertificate(data, language = 'en') {
        // Preload all images first
        await this.preloadImages();

        // Get translations for the specified language
        const texts = this.translations[language] || this.translations.en;

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Set canvas size (3:4 ratio, high resolution)
        const width = 1200;
        const height = 1600;
        canvas.width = width;
        canvas.height = height;

        // Background gradient
        const gradient = ctx.createLinearGradient(0, 0, 0, height);
        gradient.addColorStop(0, '#F9F4F3');
        gradient.addColorStop(0.5, '#FFFFFF');
        gradient.addColorStop(1, '#F9F4F3');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, width, height);

        // Background image with transparency and red filter
        const backgroundImage = await this.loadImage('assets/picture/certificate.png');
        if (backgroundImage) {
            // Save current context state
            ctx.save();

            // Apply red tint filter using composite operations
            ctx.globalCompositeOperation = 'multiply';
            ctx.fillStyle = '#FFE5E5'; // Very light red tint
            ctx.fillRect(0, 0, width, height);

            // Reset composite operation and apply transparency
            ctx.globalCompositeOperation = 'source-over';
            ctx.globalAlpha = 0.1; // Very subtle background

            // Calculate scaling to cover the entire canvas while maintaining aspect ratio
            const imgAspect = backgroundImage.width / backgroundImage.height;
            const canvasAspect = width / height;

            let drawWidth, drawHeight, drawX, drawY;

            if (imgAspect > canvasAspect) {
                // Image is wider than canvas ratio - fit to height
                drawHeight = height;
                drawWidth = height * imgAspect;
                drawX = (width - drawWidth) / 2;
                drawY = 0;
            } else {
                // Image is taller than canvas ratio - fit to width
                drawWidth = width;
                drawHeight = width / imgAspect;
                drawX = 0;
                drawY = (height - drawHeight) / 2;
            }

            // Draw the background image
            ctx.drawImage(backgroundImage, drawX, drawY, drawWidth, drawHeight);

            // Apply additional red overlay for theme consistency
            ctx.globalCompositeOperation = 'overlay';
            ctx.globalAlpha = 0.08;
            ctx.fillStyle = '#E53512'; // SnowNavi theme red
            ctx.fillRect(0, 0, width, height);

            // Restore context state
            ctx.restore();
        }
        
        // Decorative border
        ctx.strokeStyle = '#E53512';
        ctx.lineWidth = 12;
        this.drawRoundedRect(ctx, 30, 30, width - 60, height - 60, 20);
        ctx.stroke();
        
        // Inner decorative border
        ctx.strokeStyle = '#9ED4E7';
        ctx.lineWidth = 6;
        this.drawRoundedRect(ctx, 50, 50, width - 100, height - 100, 15);
        ctx.stroke();
        
        // Header section - SnowNavi branding at top
        // SnowNavi logo at top
        const headerLogo = await this.loadImage('assets/picture/snownavi_logo.png');
        if (headerLogo) {
            const headerLogoSize = 100;
            const headerLogoX = width / 2 - headerLogoSize / 2 - 400;
            ctx.drawImage(headerLogo, headerLogoX, 80, headerLogoSize, headerLogoSize);
        }

        // SnowNavi name
        ctx.fillStyle = '#E53512';
        ctx.font = 'bold 32px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(texts.companyName, width / 2, 120);

        // Professional tagline
        ctx.fillStyle = '#2F2F2F';
        ctx.font = '24px Arial';
        ctx.fillText(texts.tagline, width / 2, 160);

        // Certificate title
        ctx.fillStyle = '#E53512';
        ctx.font = 'bold 56px Arial';
        ctx.fillText(texts.certificateTitle, width / 2, 310);

        ctx.font = 'bold 32px Arial';
        ctx.fillText(texts.certificateSubtitle, width / 2, 350);

        // Student information section
        ctx.fillStyle = '#2F2F2F';
        ctx.font = '28px Arial';
        ctx.fillText(texts.certifiesText, width / 2, 410);

        // Member name (prominent, right after "This certifies that")
        ctx.fillStyle = '#E53512';
        ctx.font = 'bold 48px Arial';
        ctx.fillText(data.memberName, width / 2, 470);

        // Member ID
        ctx.fillStyle = '#2F2F2F';
        ctx.font = '24px Arial';
        ctx.fillText(`${texts.memberIdText} ${data.memberId}`, width / 2, 510);

        // Course information
        ctx.font = '28px Arial';
        ctx.fillText(texts.participatedText, width / 2, 570);

        ctx.fillStyle = '#E53512';
        ctx.font = 'bold 36px Arial';
        ctx.fillText(data.activityName, width / 2, 620);

        ctx.fillStyle = '#2F2F2F';
        ctx.font = '22px Arial';
        ctx.fillText(`${texts.courseDateText} ${data.activityDate}`, width / 2, 660);
        
        // Skills progress section
        if (data.feedback && data.feedback.skillAssessment) {
            ctx.fillStyle = '#2F2F2F';
            ctx.font = 'bold 28px Arial';
            ctx.fillText(texts.skillsProgressText, width / 2, 730);

            this.drawSkillProgress(ctx, 150, 760, width - 300, 30, data.feedback.skillAssessment, language);

            const completedSkills = data.feedback.skillAssessment.completedSkills || [];
            const totalSkills = 25;
            const completionRate = Math.round((completedSkills.length / totalSkills) * 100);

            ctx.fillStyle = '#28a745';
            ctx.font = 'bold 32px Arial';
            ctx.fillText(`${texts.overallProgressText} ${completedSkills.length}/${totalSkills} ${texts.skillsText} (${completionRate}%)`, width / 2, 880);
        }
        
        // Member QR Code (without frame)
        const memberQRY = 950;
        const memberQRSize = 100;
        const memberQRX = width / 2 - memberQRSize / 2;

        ctx.fillStyle = '#2F2F2F';
        ctx.font = 'bold 20px Arial';
        ctx.fillText(texts.memberVerificationText, width / 2, memberQRY - 10);

        try {
            const memberQR = await this.generateMemberQRCode(data.memberId, memberQRSize);
            if (memberQR) {
                ctx.drawImage(memberQR, memberQRX, memberQRY, memberQRSize, memberQRSize);
            } else {
                const fallbackQR = this.generateMemberQRPattern(data.memberId, memberQRSize);
                ctx.drawImage(fallbackQR, memberQRX, memberQRY, memberQRSize, memberQRSize);
            }
        } catch (error) {
            console.warn('Failed to generate member QR code, using fallback');
            const fallbackQR = this.generateMemberQRPattern(data.memberId, memberQRSize);
            ctx.drawImage(fallbackQR, memberQRX, memberQRY, memberQRSize, memberQRSize);
        }

        // QR Code label
        ctx.fillStyle = '#717171';
        ctx.font = '14px Arial';
        ctx.fillText(texts.scanToVerifyText, width / 2, memberQRY + memberQRSize + 20);
        
        // Social media section (moved up, no SnowNavi branding here since it's at top)
        const socialSectionY = 1110 + 80;

        // Social media section
        ctx.fillStyle = '#2F2F2F';
        ctx.font = 'bold 18px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(texts.followUsText, width / 2, socialSectionY);

        // Social media QR codes
        const socialQRSize = 70;
        const socialY = socialSectionY + 20;

        // WeChat QR
        const wechatQR = await this.loadImage('assets/picture/wechat_qrcode.jpg');
        if (wechatQR) {
            ctx.drawImage(wechatQR, width / 2 - 90, socialY, socialQRSize, socialQRSize);
        } else {
            ctx.fillStyle = '#2F2F2F';
            ctx.fillRect(width / 2 - 90, socialY, socialQRSize, socialQRSize);
        }
        ctx.fillStyle = '#717171';
        ctx.font = '14px Arial';
        ctx.fillText(texts.wechatText, width / 2 - 55, socialY + socialQRSize + 15);
        ctx.fillText(texts.socialAccountText, width / 2 - 55, socialY + socialQRSize + 30);

        // Xiaohongshu QR
        const xhsQR = await this.loadImage('assets/picture/xiaohongshu_qrcode.jpg');
        if (xhsQR) {
            ctx.drawImage(xhsQR, width / 2 + 20, socialY, socialQRSize, socialQRSize);
        } else {
            ctx.fillStyle = '#2F2F2F';
            ctx.fillRect(width / 2 + 20, socialY, socialQRSize, socialQRSize);
        }
        ctx.fillStyle = '#717171';
        ctx.font = '14px Arial';
        ctx.fillText(texts.xiaohongshuText, width / 2 + 55, socialY + socialQRSize + 15);
        ctx.fillText(texts.socialAccountText, width / 2 + 55, socialY + socialQRSize + 30);

        // Contact information (moved below social media QR codes)
        ctx.fillStyle = '#717171';
        ctx.font = '18px Arial';
        ctx.fillText(texts.contactText, width / 2, socialY + socialQRSize + 60);
        
        // Footer
        const footer_offset = 120;
        ctx.fillStyle = '#717171';
        ctx.font = '16px Arial';
        const currentDate = new Date().toLocaleDateString();
        ctx.fillText(`${texts.issuedText} ${currentDate}`, width / 2, 1320 + footer_offset);

        ctx.font = '14px Arial';
        ctx.fillText(texts.acknowledgmentText1, width / 2, 1350 + footer_offset);
        ctx.fillText(texts.acknowledgmentText2, width / 2, 1370 + footer_offset);

        ctx.font = '12px Arial';
        ctx.fillText(texts.copyrightText, width / 2, 1410 + footer_offset);
        
        // Decorative elements
        ctx.fillStyle = '#9ED4E7';

        // Corner decorations
        ctx.beginPath();
        ctx.arc(120, 120, 15, 0, 2 * Math.PI);
        ctx.fill();

        ctx.beginPath();
        ctx.arc(width - 120, 120, 15, 0, 2 * Math.PI);
        ctx.fill();

        ctx.beginPath();
        ctx.arc(120, height - 120, 15, 0, 2 * Math.PI);
        ctx.fill();

        ctx.beginPath();
        ctx.arc(width - 120, height - 120, 15, 0, 2 * Math.PI);
        ctx.fill();

        // Additional decorative elements around logo
        ctx.fillStyle = '#E53512';
        ctx.globalAlpha = 0.1;
        ctx.beginPath();
        ctx.arc(width / 2 - 150, 300, 30, 0, 2 * Math.PI);
        ctx.fill();

        ctx.beginPath();
        ctx.arc(width / 2 + 150, 300, 30, 0, 2 * Math.PI);
        ctx.fill();

        ctx.globalAlpha = 1.0;
        
        return canvas;
    }
}

// Export for use in main page
window.CertificateGenerator = CertificateGenerator;
