<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Member Admin Panel</title>

  <!-- Favicon -->
  <link rel="icon" type="image/png" sizes="32x32" href="assets/picture/snownavi_logo.png">
  <link rel="icon" type="image/png" sizes="16x16" href="assets/picture/snownavi_logo.png">
  <link rel="shortcut icon" href="assets/picture/snownavi_logo.png">

  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      background: #f7f7f7;
      height: 100vh;
      display: flex;
      flex-direction: column;
    }
    h1 {
      color: #E53512;
      padding: 1rem;
      margin: 0;
    }
    .lang-tabs {
      padding: 0 1rem;
      margin-bottom: 1rem;
    }
    .lang-tabs button {
      margin-right: 1rem;
      padding: 0.5rem 1rem;
      background: #eee;
      border: 1px solid #ccc;
      cursor: pointer;
    }
    .lang-tabs button.active {
      background: #E53512;
      color: white;
    }
    .main-container {
      display: flex;
      flex: 1;
      overflow: hidden;
    }
    .member-list {
      width: 250px;
      background: #fff;
      border-right: 1px solid #ddd;
      overflow-y: auto;
      padding: 1rem;
    }
    .member-item {
      padding: 0.5rem;
      cursor: pointer;
      border-radius: 4px;
      margin-bottom: 0.25rem;
    }
    .member-item:hover {
      background: #f0f0f0;
    }
    .member-item.active {
      background: #E53512;
      color: white;
    }
    .form-container {
      flex: 1;
      padding: 1rem;
      overflow-y: auto;
    }
    .member-block {
      background: white;
      padding: 1rem;
      margin-bottom: 1rem;
      border-radius: 8px;
      box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
    }
    label {
      font-weight: bold;
      display: block;
      margin-top: 1rem;
    }
    input, textarea, select {
      width: 100%;
      padding: 0.5rem;
      margin-top: 0.2rem;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    textarea {
      min-height: 100px;
    }
    .save-btn, .add-btn, .delete-btn {
      background: #E53512;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      border-radius: 5px;
      cursor: pointer;
      margin: 1rem 0;
    }
    .delete-btn {
      background: #ff4444;
    }
    .add-btn {
      background: #4CAF50;
    }
    .actions {
      padding: 0.5rem 1rem;
      background: #fff;
      border-top: 1px solid #ddd;
      display: flex;
      justify-content: space-between;
    }
    .no-selection {
      display: flex;
      height: 100%;
      align-items: center;
      justify-content: center;
      color: #777;
      font-size: 1.2rem;
    }
    .status-badge {
      display: inline-block;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.8rem;
      margin-left: 0.5rem;
    }
    .status-active {
      background-color: #e6f7e6;
      color: #2e7d32;
    }
    .status-expired {
      background-color: #ffebee;
      color: #c62828;
    }
    .custom-fields {
      margin-top: 1rem;
      border-top: 1px solid #eee;
      padding-top: 1rem;
    }
    .custom-field {
      display: flex;
      margin-bottom: 0.5rem;
      align-items: center;
    }
    .custom-field input {
      margin-right: 0.5rem;
    }
    .custom-field button {
      background: #ff4444;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      padding: 0.25rem 0.5rem;
    }
    .add-field-btn {
      background: #4CAF50;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;
      margin-top: 0.5rem;
    }
    .back-link {
      display: inline-block;
      margin: 1rem;
      color: #E53512;
      text-decoration: none;
    }
    .back-link:hover {
      text-decoration: underline;
    }
    /* Authentication styles */
    .auth-container {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.9);
      z-index: 1000;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }
    .auth-message {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      text-align: center;
      max-width: 400px;
    }
    .auth-message h2 {
      color: #E53512;
      margin-top: 0;
    }
    .auth-btn {
      background: #E53512;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      border-radius: 5px;
      cursor: pointer;
      margin-top: 1rem;
      text-decoration: none;
      display: inline-block;
    }
    .user-info {
      display: flex;
      align-items: center;
      position: absolute;
      top: 1rem;
      right: 1rem;
    }
    .user-info img {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      margin-right: 0.5rem;
    }
    .logout-btn {
      background: none;
      border: none;
      color: #E53512;
      cursor: pointer;
      margin-left: 1rem;
      text-decoration: underline;
    }

    /* Password button styling */
    .password-btn {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;
      font-size: 1rem;
      margin-left: 0.5rem;
    }

    .password-btn:hover {
      background-color: #45a049;
    }

    /* Modal styling */
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
    }

    .modal-content {
      background-color: #fff;
      margin: 10% auto;
      padding: 20px;
      border-radius: 8px;
      width: 80%;
      max-width: 600px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .close-modal {
      color: #aaa;
      float: right;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
    }

    .close-modal:hover {
      color: #000;
    }

    .password-list {
      margin-top: 1rem;
      max-height: 400px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 0.5rem;
    }

    .password-item {
      padding: 0.5rem;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
    }

    .password-item:last-child {
      border-bottom: none;
    }

    /* Mobile responsive styles */
    @media (max-width: 768px) {
      body {
        padding: 0;
      }

      h1 {
        font-size: 1.5rem;
        text-align: center;
        margin: 0.5rem;
        padding: 0 1rem;
      }

      .back-link {
        margin: 0.5rem 1rem !important;
        font-size: 0.9rem;
      }

      .lang-tabs {
        padding: 0 1rem;
        margin-bottom: 0.5rem;
      }

      .lang-tabs button {
        margin-right: 0.5rem;
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
      }

      .main-container {
        flex-direction: column;
        height: auto;
      }

      .member-list {
        width: 100%;
        max-height: 40vh;
        border-right: none;
        border-bottom: 1px solid #ddd;
      }

      .member-item {
        padding: 0.75rem;
        font-size: 0.9rem;
      }

      .form-container {
        padding: 0.5rem;
      }

      .member-block {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
      }

      .member-block h3 {
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
      }

      input, textarea, select {
        padding: 0.6rem;
        font-size: 0.9rem;
      }

      label {
        font-size: 0.9rem;
        margin-top: 0.75rem;
      }

      .save-btn, .add-btn, .delete-btn, .password-btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
        margin: 0.5rem 0.25rem 0.5rem 0;
      }

      .actions {
        padding: 0.75rem;
        flex-direction: column;
        gap: 0.5rem;
      }

      .actions button {
        width: 100%;
        margin: 0.25rem 0;
      }

      .custom-field {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
      }

      .custom-field input {
        margin-right: 0;
        margin-bottom: 0.25rem;
      }

      .custom-field button {
        align-self: flex-start;
        padding: 0.4rem 0.8rem;
      }

      .status-badge {
        display: block;
        margin: 0.5rem 0;
        text-align: center;
      }

      .user-info {
        position: static;
        justify-content: center;
        margin: 0.5rem;
        padding: 0.5rem;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
      }

      .modal-content {
        width: 95%;
        margin: 5% auto;
        padding: 1rem;
      }

      .password-list {
        max-height: 300px;
      }

      .password-item {
        flex-direction: column;
        gap: 0.25rem;
        padding: 0.75rem;
      }

      .no-selection {
        font-size: 1rem;
        padding: 2rem 1rem;
        text-align: center;
      }
    }

    @media (max-width: 480px) {
      h1 {
        font-size: 1.3rem;
      }

      .lang-tabs button {
        padding: 0.3rem 0.6rem;
        font-size: 0.8rem;
        margin-right: 0.3rem;
      }

      .member-list {
        max-height: 35vh;
      }

      .member-item {
        padding: 0.5rem;
        font-size: 0.85rem;
      }

      .member-block {
        padding: 0.5rem;
      }

      .member-block h3 {
        font-size: 1rem;
      }

      input, textarea, select {
        padding: 0.5rem;
        font-size: 0.85rem;
      }

      label {
        font-size: 0.85rem;
      }

      .save-btn, .add-btn, .delete-btn, .password-btn {
        padding: 0.5rem 0.8rem;
        font-size: 0.85rem;
      }

      .actions {
        padding: 0.5rem;
      }

      .custom-field button {
        padding: 0.3rem 0.6rem;
        font-size: 0.8rem;
      }

      .add-field-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
      }

      .modal-content {
        padding: 0.75rem;
      }

      .password-item {
        padding: 0.5rem;
        font-size: 0.85rem;
      }
    }
  </style>
</head>
<body>
  <!-- Authentication overlay -->
  <div class="auth-container" id="auth-container">
    <div class="auth-message">
      <h2>Authentication Required</h2>
      <p>You need to be logged in to access this page.</p>
      <a href="login.html" class="auth-btn">Go to Login</a>
    </div>
  </div>

  <div class="user-info" id="user-info"></div>

  <a href="admin.html" class="back-link">← Back to Admin Panel</a>

  <h1>SnowNavi Member Management</h1>
  <div class="lang-tabs">
    <button onclick="switchLang('en')" id="tab-en">English</button>
    <button onclick="switchLang('zh')" id="tab-zh">中文</button>
    <button onclick="switchLang('nl')" id="tab-nl">Nederlands</button>
  </div>

  <div class="main-container">
    <div class="member-list" id="member-list"></div>
    <div class="form-container" id="form-container">
      <div class="no-selection">Select a member from the left panel or add a new member</div>
    </div>
  </div>

  <div class="actions">
    <button class="add-btn" onclick="addNewMember()">Add New Member</button>
    <button class="save-btn" onclick="saveMembers()">Save All Changes</button>
    <button class="password-btn" onclick="generateAllPasswords()">Generate All Passwords</button>
  </div>

  <!-- Password Results Modal -->
  <div id="password-modal" class="modal">
    <div class="modal-content">
      <span class="close-modal">&times;</span>
      <h2>Generated Passwords</h2>
      <p>The following passwords have been generated for members:</p>
      <div class="password-list" id="password-list"></div>
    </div>
  </div>

  <script>
    let currentLang = 'en';
    let members = {};
    let selectedMember = null;
    let customFields = ['gender']; // Default custom fields

    // Function to format date as dd/mm/yyyy
    function formatDateDDMMYYYY(date) {
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = date.getFullYear();
      return `${day}/${month}/${year}`;
    }

    // Function to parse date from dd/mm/yyyy format
    function parseDateDDMMYYYY(dateStr) {
      const [day, month, year] = dateStr.split('/').map(part => parseInt(part, 10));
      return new Date(year, month - 1, day);
    }

    // Fetch configuration and check authentication
    async function checkAuth() {
      try {
        // Fetch the authorized email from the server
        const response = await fetch('/api/config');
        if (!response.ok) {
          throw new Error('Failed to fetch configuration');
        }

        const config = await response.json();
        const authorizedEmail = config.authorizedEmail;

        const auth = JSON.parse(localStorage.getItem('snownavi_auth') || '{}');
        const authContainer = document.getElementById('auth-container');
        const userInfoContainer = document.getElementById('user-info');

        // Check if auth exists and is not expired
        if (auth.email === authorizedEmail && auth.expiresAt && auth.expiresAt > Date.now()) {
          // User is authenticated
          authContainer.style.display = 'none';

          // Display user info
          userInfoContainer.innerHTML = `
            <img src="${auth.picture}" alt="Profile">
            <span>${auth.name}</span>
            <button class="logout-btn" onclick="logout()">Logout</button>
          `;

          // Load members
          loadMembers();
        } else {
          // User is not authenticated, show auth container
          authContainer.style.display = 'flex';
          userInfoContainer.innerHTML = '';
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
        document.getElementById('auth-container').style.display = 'flex';
        document.querySelector('.auth-message p').textContent = 'Error loading configuration. Please try again later.';
      }
    }

    // Logout function
    function logout() {
      localStorage.removeItem('snownavi_auth');
      window.location.href = 'login.html';
    }

    async function loadMembers() {
      try {
        const res = await fetch('data/members.json');
        members = await res.json();

        // Standardize validity period format for all members
        standardizeValidityPeriods();

        document.getElementById(`tab-${currentLang}`).classList.add('active');
        renderMemberList();
      } catch (error) {
        console.error('Error loading members:', error);
        alert('Failed to load member data. Please try again later.');
      }
    }

    // Function to check if validity period has expired
    function checkValidityPeriodExpired(validityPeriodStr) {
      try {
        // Check if the validity period is in the dd/mm/yyyy - dd/mm/yyyy format
        const match = validityPeriodStr.match(/^(\d{2}\/\d{2}\/\d{4}) - (\d{2}\/\d{2}\/\d{4})$/);
        if (match) {
          const endDateStr = match[2];
          const endDate = parseDateDDMMYYYY(endDateStr);

          // Set time to end of day (23:59:59)
          endDate.setHours(23, 59, 59, 999);

          // Compare with current date
          const today = new Date();
          return today > endDate;
        }

        // If the format doesn't match, try to extract the end date from other formats
        // This is a fallback for any validity periods not yet converted to dd/mm/yyyy format
        const parts = validityPeriodStr.split(' - ');
        if (parts.length === 2) {
          // Try to extract a year from the end date part
          const yearMatch = parts[1].match(/\d{4}/);
          if (yearMatch) {
            const year = parseInt(yearMatch[0], 10);
            // If the year is in the past, consider it expired
            return new Date().getFullYear() > year;
          }
        }

        // If we can't determine, default to not expired
        return false;
      } catch (e) {
        console.error('Error checking validity period:', e);
        return false;
      }
    }

    // Function to standardize all validity periods to dd/mm/yyyy format
    function standardizeValidityPeriods() {
      Object.keys(members).forEach(memberId => {
        const member = members[memberId];

        // Convert old format (object with language keys) to new format (single string)
        if (member.validityPeriod && typeof member.validityPeriod === 'object') {
          let validityValue = '';

          // Try to find a non-empty value in any language
          for (const lang of ['en', 'zh', 'nl']) {
            if (member.validityPeriod[lang]) {
              validityValue = member.validityPeriod[lang];
              break;
            }
          }

          // If we found a value but it's not in dd/mm/yyyy format, convert it
          if (validityValue && !validityValue.match(/^\d{2}\/\d{2}\/\d{4} - \d{2}\/\d{2}\/\d{4}$/)) {
            try {
              let startDate, endDate;

              // Try to parse different formats
              if (validityValue.includes(',')) {
                // Format: "January 1, 2025 - December 31, 2025"
                const parts = validityValue.split(' - ');
                if (parts.length === 2) {
                  const startParts = parts[0].split(' ');
                  const endParts = parts[1].split(' ');

                  if (startParts.length >= 3 && endParts.length >= 3) {
                    const startMonth = new Date(Date.parse(`${startParts[0]} 1, 2000`)).getMonth();
                    const startDay = parseInt(startParts[1].replace(',', ''));
                    const startYear = parseInt(startParts[2]);

                    const endMonth = new Date(Date.parse(`${endParts[0]} 1, 2000`)).getMonth();
                    const endDay = parseInt(endParts[1].replace(',', ''));
                    const endYear = parseInt(endParts[2]);

                    startDate = new Date(startYear, startMonth, startDay);
                    endDate = new Date(endYear, endMonth, endDay);
                  }
                }
              } else if (validityValue.includes('年')) {
                // Format: "2025年1月1日 - 2025年12月31日"
                const parts = validityValue.split(' - ');
                if (parts.length === 2) {
                  // Extract year, month, day from Chinese format
                  const startMatch = parts[0].match(/(\d+)年(\d+)月(\d+)日/);
                  const endMatch = parts[1].match(/(\d+)年(\d+)月(\d+)日/);

                  if (startMatch && endMatch) {
                    startDate = new Date(parseInt(startMatch[1]), parseInt(startMatch[2]) - 1, parseInt(startMatch[3]));
                    endDate = new Date(parseInt(endMatch[1]), parseInt(endMatch[2]) - 1, parseInt(endMatch[3]));
                  }
                }
              } else if (validityValue.includes('januari') || validityValue.includes('december')) {
                // Format: "1 januari 2025 - 31 december 2025"
                const parts = validityValue.split(' - ');
                if (parts.length === 2) {
                  const startParts = parts[0].split(' ');
                  const endParts = parts[1].split(' ');

                  if (startParts.length >= 3 && endParts.length >= 3) {
                    // Dutch month names to numbers
                    const monthMap = {
                      'januari': 0, 'februari': 1, 'maart': 2, 'april': 3, 'mei': 4, 'juni': 5,
                      'juli': 6, 'augustus': 7, 'september': 8, 'oktober': 9, 'november': 10, 'december': 11
                    };

                    const startDay = parseInt(startParts[0]);
                    const startMonth = monthMap[startParts[1].toLowerCase()];
                    const startYear = parseInt(startParts[2]);

                    const endDay = parseInt(endParts[0]);
                    const endMonth = monthMap[endParts[1].toLowerCase()];
                    const endYear = parseInt(endParts[2]);

                    if (!isNaN(startDay) && startMonth !== undefined && !isNaN(startYear) &&
                        !isNaN(endDay) && endMonth !== undefined && !isNaN(endYear)) {
                      startDate = new Date(startYear, startMonth, startDay);
                      endDate = new Date(endYear, endMonth, endDay);
                    }
                  }
                }
              }

              // If we successfully parsed the dates, convert to standard format
              if (startDate && endDate && !isNaN(startDate) && !isNaN(endDate)) {
                const standardStartDate = formatDateDDMMYYYY(startDate);
                const standardEndDate = formatDateDDMMYYYY(endDate);
                validityValue = `${standardStartDate} - ${standardEndDate}`;
              }
            } catch (e) {
              console.error(`Error standardizing date format for ${memberId}:`, e);
              // Keep the original if there's an error
            }
          }

          // Set the standardized value as the new validityPeriod
          member.validityPeriod = validityValue;
        }

        // Check if the validity period has expired and update member status
        if (member.isActive && member.validityPeriod) {
          const isExpired = checkValidityPeriodExpired(member.validityPeriod);
          if (isExpired) {
            console.log(`Member ${memberId} validity period has expired. Updating status.`);
            member.isActive = false;
          }
        }
      });
    }

    function switchLang(lang) {
      currentLang = lang;
      document.querySelectorAll('.lang-tabs button').forEach(btn => btn.classList.remove('active'));
      document.getElementById(`tab-${lang}`).classList.add('active');
      renderMemberList();
      if (selectedMember) {
        renderMemberForm(selectedMember);
      }
    }

    function renderMemberList() {
      const memberList = document.getElementById('member-list');
      memberList.innerHTML = '';

      Object.keys(members).forEach(memberId => {
        const member = members[memberId];
        const memberItem = document.createElement('div');
        memberItem.className = 'member-item';
        memberItem.textContent = member.name;

        // Add status badge
        const statusBadge = document.createElement('span');
        statusBadge.className = `status-badge ${member.isActive ? 'status-active' : 'status-expired'}`;
        statusBadge.textContent = member.isActive ? 'Active' : 'Expired';
        memberItem.appendChild(statusBadge);

        memberItem.dataset.memberId = memberId;
        memberItem.onclick = () => selectMember(memberId);

        if (selectedMember === memberId) {
          memberItem.classList.add('active');
        }

        memberList.appendChild(memberItem);
      });
    }

    function selectMember(memberId) {
      selectedMember = memberId;
      renderMemberList();
      renderMemberForm(memberId);
    }

    function renderMemberForm(memberId) {
      const container = document.getElementById('form-container');
      container.innerHTML = '';

      const member = members[memberId];
      const div = document.createElement('div');
      div.className = 'member-block';

      // Member ID and Name header
      const titleHeader = document.createElement('h2');
      titleHeader.textContent = `${member.name} (${memberId})`;
      div.appendChild(titleHeader);

      // Member ID (read-only for existing members, editable for new members)
      const idLabel = document.createElement('label');
      idLabel.textContent = 'Member ID';
      div.appendChild(idLabel);

      const idInput = document.createElement('input');
      idInput.value = memberId;

      // Check if this is a newly created member (name is "New Member")
      const isNewMember = member.name === 'New Member';

      // Make the ID field read-only for existing members
      idInput.readOnly = !isNewMember;
      idInput.style.backgroundColor = isNewMember ? '#fff' : '#f5f5f5';

      // Add a tooltip for the ID format
      idInput.title = 'Format: SN + year + 4-digit sequence number (e.g., **********)';

      // Add event listener to update the member ID if changed
      idInput.oninput = e => {
        if (isNewMember) {
          // If the ID is changed, we need to update the object key in the members object
          const newId = e.target.value;

          // Don't allow empty IDs
          if (!newId) {
            e.target.value = memberId;
            return;
          }

          // Check if the new ID already exists
          if (newId !== memberId && members[newId]) {
            alert('A member with this ID already exists.');
            e.target.value = memberId;
            return;
          }

          // Validate the ID format
          if (!validateMemberId(newId)) {
            alert('Invalid member ID format. Please use the format: SN + year + 4-digit sequence number (e.g., **********)');
            e.target.value = memberId;
            return;
          }

          // Update the member object with the new ID
          member.id = newId;

          // Create a copy of the member object with the new ID
          members[newId] = member;

          // Delete the old member object
          if (newId !== memberId) {
            delete members[memberId];

            // Update the selected member
            selectedMember = newId;

            // Refresh the member list
            renderMemberList();
          }
        }
      };

      div.appendChild(idInput);

      // Member Name
      const nameLabel = document.createElement('label');
      nameLabel.textContent = 'Name';
      div.appendChild(nameLabel);

      const nameInput = document.createElement('input');
      nameInput.value = member.name;
      nameInput.oninput = e => member.name = e.target.value;
      div.appendChild(nameInput);

      // Membership Status
      const statusLabel = document.createElement('label');
      statusLabel.textContent = 'Membership Status';
      div.appendChild(statusLabel);

      const statusSelect = document.createElement('select');
      const activeOption = document.createElement('option');
      activeOption.value = 'true';
      activeOption.textContent = 'Active';
      statusSelect.appendChild(activeOption);

      const expiredOption = document.createElement('option');
      expiredOption.value = 'false';
      expiredOption.textContent = 'Expired';
      statusSelect.appendChild(expiredOption);

      statusSelect.value = member.isActive.toString();
      statusSelect.onchange = e => {
        const newStatus = e.target.value === 'true';
        const oldStatus = member.isActive;
        member.isActive = newStatus;

        // If status is changing from inactive to active (Full Membership)
        if (!oldStatus && newStatus) {
          // Generate a one-year validity period from today
          const today = new Date();
          const oneYearLater = new Date(today);
          oneYearLater.setFullYear(today.getFullYear() + 1);

          // Format dates in dd/mm/yyyy format
          const startDate = formatDateDDMMYYYY(today);
          const endDate = formatDateDDMMYYYY(oneYearLater);

          // Set validity period as a single string
          member.validityPeriod = `${startDate} - ${endDate}`;

          // Update the validity input if it exists
          const validityInput = document.getElementById('validity-input');
          if (validityInput) {
            validityInput.value = member.validityPeriod || '';
          }
        }
      };
      div.appendChild(statusSelect);

      // Validity Period
      const validityLabel = document.createElement('label');
      validityLabel.textContent = 'Validity Period';
      div.appendChild(validityLabel);

      const validityInput = document.createElement('input');
      validityInput.id = 'validity-input';

      // If validityPeriod is an object (old format), get the first non-empty value or convert to string
      if (typeof member.validityPeriod === 'object') {
        let validityValue = '';
        for (const lang of ['en', 'zh', 'nl']) {
          if (member.validityPeriod[lang]) {
            validityValue = member.validityPeriod[lang];
            break;
          }
        }
        // Convert to string format
        member.validityPeriod = validityValue;
      }

      validityInput.value = member.validityPeriod || '';
      validityInput.placeholder = 'dd/mm/yyyy - dd/mm/yyyy';
      validityInput.oninput = e => member.validityPeriod = e.target.value;
      div.appendChild(validityInput);

      // Password Field (read-only)
      if (member.password) {
        const passwordLabel = document.createElement('label');
        passwordLabel.textContent = 'Password (Hashed)';
        div.appendChild(passwordLabel);

        const passwordContainer = document.createElement('div');
        passwordContainer.className = 'password-container';

        const passwordInput = document.createElement('input');
        passwordInput.type = 'text';
        passwordInput.value = member.password;
        passwordInput.readOnly = true;
        passwordInput.style.backgroundColor = '#f5f5f5';
        passwordInput.style.width = 'calc(100% - 110px)';
        passwordContainer.appendChild(passwordInput);

        // Add button to regenerate individual password
        const regenerateBtn = document.createElement('button');
        regenerateBtn.textContent = 'Regenerate';
        regenerateBtn.className = 'regenerate-btn';
        regenerateBtn.style.marginLeft = '10px';
        regenerateBtn.onclick = async () => {
          try {
            // Call the API to generate a new password for this member
            const response = await fetch('/api/members/generate-passwords', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' }
            });

            if (!response.ok) {
              throw new Error('Failed to generate password');
            }

            const result = await response.json();

            // Find this member's new password
            const memberData = result.members.find(m => m.id === memberId);
            if (memberData) {
              // Show the new password
              alert(`New password for ${member.name}: ${memberData.password}`);

              // Reload members to get the updated data
              await loadMembers();

              // Re-render the form
              renderMemberForm(memberId);
            }
          } catch (error) {
            console.error('Error regenerating password:', error);
            alert('Failed to regenerate password. Please try again later.');
          }
        };
        passwordContainer.appendChild(regenerateBtn);

        div.appendChild(passwordContainer);
      }

      // Custom Fields Section
      const customFieldsSection = document.createElement('div');
      customFieldsSection.className = 'custom-fields';

      const customFieldsHeader = document.createElement('h3');
      customFieldsHeader.textContent = 'Custom Fields';
      customFieldsSection.appendChild(customFieldsHeader);

      // Render existing custom fields
      customFields.forEach(field => {
        const fieldContainer = document.createElement('div');
        fieldContainer.className = 'custom-field';

        const fieldLabel = document.createElement('label');
        fieldLabel.textContent = field.charAt(0).toUpperCase() + field.slice(1);
        fieldContainer.appendChild(fieldLabel);

        const fieldInput = document.createElement('input');
        fieldInput.value = member[field] || '';
        fieldInput.oninput = e => member[field] = e.target.value;
        fieldContainer.appendChild(fieldInput);

        customFieldsSection.appendChild(fieldContainer);
      });

      // Add new custom field button
      const addFieldBtn = document.createElement('button');
      addFieldBtn.className = 'add-field-btn';
      addFieldBtn.textContent = 'Add Custom Field';
      addFieldBtn.onclick = () => addCustomField(member);
      customFieldsSection.appendChild(addFieldBtn);

      div.appendChild(customFieldsSection);

      // Delete button
      const deleteBtn = document.createElement('button');
      deleteBtn.textContent = 'Delete Member';
      deleteBtn.className = 'delete-btn';
      deleteBtn.onclick = () => deleteMember(memberId);
      div.appendChild(deleteBtn);

      container.appendChild(div);
    }

    function addCustomField(member) {
      const fieldName = prompt('Enter the name of the new field:');
      if (!fieldName) return;

      // Check if field already exists
      if (customFields.includes(fieldName)) {
        alert('This field already exists.');
        return;
      }

      // Add to custom fields list
      customFields.push(fieldName);

      // Initialize the field in the member object
      member[fieldName] = '';

      // Re-render the form
      renderMemberForm(selectedMember);
    }

    // Function to validate member ID format (SN + year + 4-digit sequence number)
    function validateMemberId(id) {
      // Check if the ID matches the pattern: SN + 4-digit year + 4-digit sequence
      const pattern = /^SN\d{4}\d{4}$/;
      return pattern.test(id);
    }

    // Function to generate a new member ID
    function generateMemberId() {
      // Get current year
      const currentYear = new Date().getFullYear();

      // Format: SN + year + 4-digit sequence number
      const prefix = `SN${currentYear}`;

      // Find the highest sequence number for the current year
      let maxSequence = 0;

      Object.keys(members).forEach(id => {
        if (id.startsWith(prefix)) {
          // Extract the sequence number (last 4 digits)
          const sequenceStr = id.substring(prefix.length);
          const sequence = parseInt(sequenceStr, 10);

          if (!isNaN(sequence) && sequence > maxSequence) {
            maxSequence = sequence;
          }
        }
      });

      // Increment the sequence number
      const newSequence = maxSequence + 1;

      // Format the sequence number with leading zeros
      const sequenceStr = newSequence.toString().padStart(4, '0');

      // Return the new member ID
      return `${prefix}${sequenceStr}`;
    }

    function addNewMember() {
      // Generate a new member ID
      const generatedId = generateMemberId();

      // Prompt the user with the generated ID (allowing them to modify it)
      const memberId = prompt('Enter a unique member ID:', generatedId);
      if (!memberId) return;

      // Check if member ID already exists
      if (members[memberId]) {
        alert('A member with this ID already exists.');
        return;
      }

      // Validate the ID format
      if (!validateMemberId(memberId)) {
        alert('Invalid member ID format. Please use the format: SN + year + 4-digit sequence number (e.g., **********)');
        addNewMember(); // Try again
        return;
      }

      // Generate a one-year validity period from today
      const today = new Date();
      const oneYearLater = new Date(today);
      oneYearLater.setFullYear(today.getFullYear() + 1);

      // Format dates in dd/mm/yyyy format
      const startDate = formatDateDDMMYYYY(today);
      const endDate = formatDateDDMMYYYY(oneYearLater);

      // Create new member object
      const newMember = {
        id: memberId,
        name: 'New Member',
        isActive: true,
        validityPeriod: `${startDate} - ${endDate}`
      };

      // Add custom fields
      customFields.forEach(field => {
        newMember[field] = '';
      });

      // Add to members object
      members[memberId] = newMember;

      // Generate a password for the new member
      fetch('/api/members/generate-passwords', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })
      .then(response => response.json())
      .then(result => {
        // Find this member's password
        const memberData = result.members.find(m => m.id === memberId);
        if (memberData) {
          // Show the password
          alert(`Password for new member ${memberId}: ${memberData.password}`);

          // Reload members to get the updated data
          loadMembers().then(() => {
            // Update the UI
            renderMemberList();
            selectMember(memberId);
          });
        }
      })
      .catch(error => {
        console.error('Error generating password:', error);
        // Still update the UI even if password generation fails
        renderMemberList();
        selectMember(memberId);
      });
    }

    function deleteMember(memberId) {
      if (confirm(`Are you sure you want to delete member ${members[memberId].name}?`)) {
        delete members[memberId];
        selectedMember = null;
        renderMemberList();
        document.getElementById('form-container').innerHTML = '<div class="no-selection">Select a member from the left panel or add a new member</div>';
      }
    }

    function saveMembers() {
      // Make sure all validity periods are in the standard format before saving
      standardizeValidityPeriods();

      // Check all members for expired validity periods
      Object.keys(members).forEach(memberId => {
        const member = members[memberId];
        if (member.isActive && member.validityPeriod) {
          const isExpired = checkValidityPeriodExpired(member.validityPeriod);
          if (isExpired) {
            console.log(`Member ${memberId} validity period has expired. Updating status.`);
            member.isActive = false;
          }
        }
      });

      fetch('data/members.json', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(members, null, 2)
      }).then(res => {
        if (res.ok) {
          alert('Saved successfully!');
          // Refresh the member list to show updated status
          renderMemberList();
          // Re-render the current member form if one is selected
          if (selectedMember) {
            renderMemberForm(selectedMember);
          }
        }
        else alert('Error saving file');
      }).catch(err => {
        console.error('Error saving members:', err);
        alert('Failed to save member data. Please try again later.');
      });
    }

    // Function to generate passwords for all members
    async function generateAllPasswords() {
      try {
        const response = await fetch('/api/members/generate-passwords', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        });

        if (!response.ok) {
          throw new Error('Failed to generate passwords');
        }

        const result = await response.json();

        // Display the generated passwords in the modal
        const passwordList = document.getElementById('password-list');
        passwordList.innerHTML = '';

        result.members.forEach(member => {
          const passwordItem = document.createElement('div');
          passwordItem.className = 'password-item';
          passwordItem.innerHTML = `
            <div><strong>${member.name}</strong> (${member.id})</div>
            <div>Password: <code>${member.password}</code></div>
          `;
          passwordList.appendChild(passwordItem);
        });

        // Show the modal
        const modal = document.getElementById('password-modal');
        modal.style.display = 'block';

        // Add close functionality to the modal
        const closeBtn = document.querySelector('.close-modal');
        closeBtn.onclick = () => {
          modal.style.display = 'none';
        };

        // Close modal when clicking outside
        window.onclick = (event) => {
          if (event.target === modal) {
            modal.style.display = 'none';
          }
        };

        // Reload members to get the updated data
        await loadMembers();

        // Re-render the current member form if one is selected
        if (selectedMember) {
          renderMemberForm(selectedMember);
        }

      } catch (error) {
        console.error('Error generating passwords:', error);
        alert('Failed to generate passwords. Please try again later.');
      }
    }

    // Initialize the page
    window.onload = () => {
      checkAuth();

      // Add event listener for modal close button
      const closeBtn = document.querySelector('.close-modal');
      closeBtn.onclick = () => {
        document.getElementById('password-modal').style.display = 'none';
      };
    };
  </script>
</body>
</html>
