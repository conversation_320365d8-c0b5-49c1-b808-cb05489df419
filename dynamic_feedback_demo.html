<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Feedback System Demo - SnowNavi</title>
    <style>
        :root {
            --main-red: #E53512;
            --bg-light: #F9F4F3;
            --text-dark: #2F2F2F;
            --text-gray: #717171;
            --contrast-white: #FFFFFF;
            --accent-blue: #9ED4E7;
            --success-green: #28a745;
            --warning-orange: #ffc107;
        }

        body { 
            font-family: 'Noto Sans SC', sans-serif; 
            margin: 2rem; 
            background: var(--bg-light);
            color: var(--text-dark);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--contrast-white);
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 3px solid var(--main-red);
        }
        h1 { color: var(--main-red); }
        .demo-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid var(--main-red);
            background: var(--bg-light);
        }
        .demo-section h3 {
            color: var(--main-red);
            margin: 0 0 1rem 0;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        .feature-card {
            background: var(--contrast-white);
            border-radius: 8px;
            padding: 1.5rem;
            border: 2px solid var(--accent-blue);
            transition: transform 0.2s ease;
        }
        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }
        .feature-card h4 {
            color: var(--main-red);
            margin-bottom: 1rem;
        }
        .workflow-step {
            display: flex;
            align-items: center;
            margin: 1rem 0;
            padding: 1rem;
            background: var(--contrast-white);
            border-radius: 8px;
            border-left: 4px solid var(--success-green);
        }
        .step-number {
            background: var(--main-red);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
            flex-shrink: 0;
        }
        .btn {
            background: var(--main-red);
            color: var(--contrast-white);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 0.5rem;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #c42e0f;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(229, 53, 18, 0.3);
        }
        .btn-secondary {
            background: var(--text-gray);
        }
        .btn-secondary:hover {
            background: #5a5a5a;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 1rem 0;
        }
        .highlight {
            background: rgba(229, 53, 18, 0.1);
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            font-weight: bold;
            color: var(--main-red);
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 0.75rem;
            text-align: left;
        }
        .comparison-table th {
            background: var(--main-red);
            color: white;
        }
        .comparison-table tr:nth-child(even) {
            background: var(--bg-light);
        }
        .status-badge {
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .badge-implemented {
            background: var(--success-green);
            color: white;
        }
        .badge-new {
            background: var(--warning-orange);
            color: white;
        }
        .architecture-diagram {
            background: var(--contrast-white);
            border: 2px solid var(--accent-blue);
            border-radius: 8px;
            padding: 2rem;
            margin: 2rem 0;
            text-align: center;
        }
        .diagram-box {
            display: inline-block;
            background: var(--bg-light);
            border: 2px solid var(--main-red);
            border-radius: 8px;
            padding: 1rem;
            margin: 0.5rem;
            min-width: 150px;
        }
        .diagram-arrow {
            font-size: 2rem;
            color: var(--main-red);
            margin: 0 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Dynamic Feedback System</h1>
            <p>Configurable feedback templates for different course types and skill levels</p>
        </div>
        
        <div class="demo-section">
            <h3>🚀 System Overview</h3>
            <p>The new dynamic feedback system allows administrators to create and manage customizable feedback templates for different course types, skill levels, and sports. This replaces the previous hard-coded feedback structure with a flexible, configurable approach.</p>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>📝 Template Management</h4>
                    <p>Create, edit, and delete feedback templates with custom skill sections and assessment criteria.</p>
                    <ul>
                        <li>Multi-language support (EN/ZH/NL)</li>
                        <li>Sport-specific templates (Snowboard/Ski)</li>
                        <li>Skill level targeting (Beginner/Intermediate/Advanced)</li>
                        <li>Custom skill sections and items</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4>🔗 Activity Integration</h4>
                    <p>Link activities to specific feedback templates for targeted skill assessment.</p>
                    <ul>
                        <li>Template selection in activity creation</li>
                        <li>Automatic template loading in feedback forms</li>
                        <li>Backward compatibility with existing data</li>
                        <li>Template preview functionality</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4>⚡ Dynamic Loading</h4>
                    <p>Feedback forms automatically adapt based on the selected activity's template.</p>
                    <ul>
                        <li>Real-time template loading</li>
                        <li>Dynamic skill section generation</li>
                        <li>Flexible assessment criteria</li>
                        <li>Consistent data structure</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>🏗️ System Architecture</h3>
            <div class="architecture-diagram">
                <div class="diagram-box">
                    <strong>Feedback Templates</strong><br>
                    <small>feedback_templates.json</small>
                </div>
                <span class="diagram-arrow">→</span>
                <div class="diagram-box">
                    <strong>Activity Management</strong><br>
                    <small>Template Selection</small>
                </div>
                <span class="diagram-arrow">→</span>
                <div class="diagram-box">
                    <strong>Dynamic Feedback</strong><br>
                    <small>Auto-generated Forms</small>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>📋 Implementation Workflow</h3>
            
            <div class="workflow-step">
                <div class="step-number">1</div>
                <div>
                    <strong>Create Feedback Templates</strong><br>
                    Use the template management interface to define skill sections, assessment criteria, and target demographics.
                </div>
            </div>
            
            <div class="workflow-step">
                <div class="step-number">2</div>
                <div>
                    <strong>Assign Templates to Activities</strong><br>
                    When creating or editing activities, select the appropriate feedback template for the course type and level.
                </div>
            </div>
            
            <div class="workflow-step">
                <div class="step-number">3</div>
                <div>
                    <strong>Dynamic Feedback Generation</strong><br>
                    The feedback form automatically loads the template structure when instructors provide student feedback.
                </div>
            </div>
            
            <div class="workflow-step">
                <div class="step-number">4</div>
                <div>
                    <strong>Student Feedback Display</strong><br>
                    Students view their feedback using the same template structure, ensuring consistency across the system.
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>🔄 Before vs After Comparison</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Aspect</th>
                        <th>Before (Hard-coded)</th>
                        <th>After (Dynamic)</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Skill Structure</td>
                        <td>Fixed 5 sections, 25 skills</td>
                        <td>Configurable sections and skills</td>
                        <td><span class="status-badge badge-implemented">✅ Implemented</span></td>
                    </tr>
                    <tr>
                        <td>Course Types</td>
                        <td>One-size-fits-all</td>
                        <td>Sport and level specific</td>
                        <td><span class="status-badge badge-implemented">✅ Implemented</span></td>
                    </tr>
                    <tr>
                        <td>Template Management</td>
                        <td>Code changes required</td>
                        <td>Admin interface</td>
                        <td><span class="status-badge badge-implemented">✅ Implemented</span></td>
                    </tr>
                    <tr>
                        <td>Activity Integration</td>
                        <td>No template selection</td>
                        <td>Template assignment per activity</td>
                        <td><span class="status-badge badge-implemented">✅ Implemented</span></td>
                    </tr>
                    <tr>
                        <td>Backward Compatibility</td>
                        <td>N/A</td>
                        <td>Existing data preserved</td>
                        <td><span class="status-badge badge-new">🔄 Ready</span></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="demo-section">
            <h3>📊 Template Data Structure</h3>
            <div class="code-block">
{
  "template_id": {
    "id": "snowboard_beginner",
    "name": { "en": "Snowboard Beginner", "zh": "单板初学者", "nl": "..." },
    "description": { "en": "...", "zh": "...", "nl": "..." },
    "targetLevel": "beginner",
    "sport": "snowboard",
    "sections": {
      "basic": {
        "title": { "en": "Basic Knowledge", "zh": "基础知识", "nl": "..." },
        "order": 1,
        "skills": {
          "equipment-intro": {
            "name": { "en": "Equipment Introduction", "zh": "...", "nl": "..." },
            "description": { "en": "...", "zh": "...", "nl": "..." },
            "order": 1
          }
        }
      }
    }
  }
}
            </div>
        </div>
        
        <div style="text-align: center; margin: 2rem 0;">
            <h3 style="color: var(--main-red); margin-bottom: 1rem;">🧪 Try the Dynamic Feedback System</h3>
            <a href="feedback_template_admin.html" target="_blank" class="btn">📝 Manage Templates</a>
            <a href="activity_admin.html" target="_blank" class="btn">📅 Configure Activities</a>
            <a href="checkin_admin.html" target="_blank" class="btn">✅ Test Feedback</a>
            <a href="student_feedback.html" target="_blank" class="btn btn-secondary">🎿 Student View</a>
        </div>
        
        <div style="background: var(--bg-light); padding: 1.5rem; border-radius: 8px; text-align: center;">
            <h4 style="color: var(--main-red); margin: 0 0 1rem 0;">🎯 Dynamic Feedback System Complete</h4>
            <p>The new system provides complete flexibility for creating course-specific feedback templates while maintaining backward compatibility with existing data. Administrators can now easily adapt the feedback system to different course types, skill levels, and sports without requiring code changes.</p>
        </div>
    </div>
</body>
</html>
