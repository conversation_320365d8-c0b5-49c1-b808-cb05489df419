<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate Feature Demo - SnowNavi</title>
    <style>
        :root {
            --main-red: #E53512;
            --bg-light: #F9F4F3;
            --text-dark: #2F2F2F;
            --text-gray: #717171;
            --contrast-white: #FFFFFF;
            --accent-blue: #9ED4E7;
        }

        body { 
            font-family: 'Noto Sans SC', sans-serif; 
            margin: 2rem; 
            background: var(--bg-light);
            color: var(--text-dark);
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: var(--contrast-white);
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 3px solid var(--main-red);
        }
        h1 { color: var(--main-red); }
        h2 { color: var(--text-dark); border-bottom: 2px solid var(--main-red); padding-bottom: 0.5rem; }
        .demo-section { 
            margin: 2rem 0; 
            padding: 1.5rem; 
            border: 1px solid rgba(229, 53, 18, 0.1);
            border-radius: 8px;
            background: var(--bg-light);
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        .feature-card {
            background: var(--contrast-white);
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid var(--main-red);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }
        .feature-card h4 {
            margin: 0 0 0.5rem 0;
            color: var(--main-red);
        }
        .highlight {
            background: var(--contrast-white);
            padding: 1rem;
            border-radius: 6px;
            border: 2px solid var(--accent-blue);
            margin: 1rem 0;
            box-shadow: 0 2px 8px rgba(158, 212, 231, 0.2);
        }
        .certificate-mockup {
            background: var(--contrast-white);
            border: 3px solid var(--main-red);
            border-radius: 12px;
            padding: 2rem;
            margin: 2rem 0;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            position: relative;
        }
        .certificate-mockup::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            border: 2px solid var(--accent-blue);
            border-radius: 8px;
            pointer-events: none;
        }
        .mockup-title {
            color: var(--main-red);
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .mockup-subtitle {
            color: var(--text-dark);
            font-size: 1.2rem;
            margin-bottom: 1rem;
        }
        .mockup-brand {
            color: var(--text-dark);
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 2rem;
        }
        .mockup-student {
            color: var(--main-red);
            font-size: 1.8rem;
            font-weight: bold;
            margin: 1rem 0;
        }
        .mockup-course {
            color: var(--text-dark);
            font-size: 1.1rem;
            margin: 1rem 0;
        }
        .mockup-skills {
            background: var(--bg-light);
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }
        .skill-bar {
            background: #e0e0e0;
            height: 20px;
            border-radius: 10px;
            margin: 0.5rem 0;
            overflow: hidden;
        }
        .skill-progress {
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }
        .mockup-qr {
            display: inline-block;
            width: 60px;
            height: 60px;
            background: var(--text-dark);
            margin: 0 1rem;
            border-radius: 4px;
        }
        .portal-link {
            background: var(--contrast-white);
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
            margin: 2rem 0;
            border: 2px solid var(--main-red);
            box-shadow: 0 2px 8px rgba(229, 53, 18, 0.1);
        }
        .portal-link a {
            color: var(--contrast-white);
            text-decoration: none;
            font-weight: bold;
            font-size: 1.1rem;
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: var(--main-red);
            border-radius: 6px;
            transition: background-color 0.2s;
            margin: 0.5rem;
        }
        .portal-link a:hover {
            background: #c42e0f;
        }
        .portal-link a.secondary {
            background: var(--accent-blue);
            color: var(--text-dark);
        }
        .portal-link a.secondary:hover {
            background: #7bc3d4;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📜 SnowNavi Course Completion Certificate</h1>
            <p>Professional digital certificates for snowboarding achievement</p>
        </div>
        
        <div class="highlight">
            <strong>New Feature:</strong> Students can now download personalized certificates showing their 
            snowboarding progress, completed skills, and SnowNavi branding with contact information.
        </div>
        
        <div class="demo-section">
            <h2>🎨 Certificate Design</h2>
            
            <div class="certificate-mockup">
                <div class="mockup-title">CERTIFICATE</div>
                <div class="mockup-subtitle">OF SNOWBOARDING ACHIEVEMENT</div>
                <div class="mockup-brand">SnowNavi Snow Club</div>
                <div style="color: var(--text-gray); margin: 1rem 0;">This certifies that</div>
                <div class="mockup-student">Zhang Wei</div>
                <div style="color: var(--text-gray); font-size: 0.9rem;">Member ID: SN20250001</div>
                <div class="mockup-course">has successfully participated in<br><strong>Beginner Snowboard Lesson</strong></div>
                
                <div class="mockup-skills">
                    <div style="font-weight: bold; margin-bottom: 1rem;">Snowboarding Skills Progress</div>
                    <div style="display: flex; justify-content: space-between; font-size: 0.8rem; margin-bottom: 0.5rem;">
                        <span>Basic</span><span>Sliding</span><span>Control</span><span>Turning</span><span>Flow</span>
                    </div>
                    <div style="display: flex; gap: 0.5rem;">
                        <div class="skill-bar" style="flex: 1;"><div class="skill-progress" style="width: 100%; background: #FF6B6B;"></div></div>
                        <div class="skill-bar" style="flex: 1;"><div class="skill-progress" style="width: 80%; background: #4ECDC4;"></div></div>
                        <div class="skill-bar" style="flex: 1;"><div class="skill-progress" style="width: 60%; background: #45B7D1;"></div></div>
                        <div class="skill-bar" style="flex: 1;"><div class="skill-progress" style="width: 40%; background: #96CEB4;"></div></div>
                        <div class="skill-bar" style="flex: 1;"><div class="skill-progress" style="width: 25%; background: #FFEAA7;"></div></div>
                    </div>
                    <div style="color: #28a745; font-weight: bold; margin-top: 1rem;">Overall Progress: 20/25 Skills (80%)</div>
                </div>
                
                <div style="margin: 1rem 0;">
                    <div class="mockup-qr"></div>
                    <div style="font-size: 0.8rem; color: var(--text-gray);">Member Verification</div>
                </div>
                
                <div style="color: var(--text-gray); font-size: 0.9rem; margin: 1rem 0;">
                    Contact: <EMAIL> | snownavi.ski
                </div>
                
                <div style="display: flex; justify-content: center; gap: 2rem; margin: 1rem 0;">
                    <div>
                        <div class="mockup-qr" style="width: 40px; height: 40px;"></div>
                        <div style="font-size: 0.7rem; color: var(--text-gray);">WeChat</div>
                    </div>
                    <div>
                        <div class="mockup-qr" style="width: 40px; height: 40px;"></div>
                        <div style="font-size: 0.7rem; color: var(--text-gray);">Xiaohongshu</div>
                    </div>
                </div>
                
                <div style="color: var(--text-gray); font-size: 0.8rem;">Certificate issued on 2025-01-27</div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>✨ Key Features</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>📐 Professional Design</h4>
                    <p>3:4 aspect ratio (1200x1600px) certificate with SnowNavi branding, decorative borders, and professional typography.</p>
                </div>
                
                <div class="feature-card">
                    <h4>👤 Student Information</h4>
                    <p>Prominently displays student name, member ID, course details, and includes a QR code for member verification.</p>
                </div>
                
                <div class="feature-card">
                    <h4>📊 Skill Progress</h4>
                    <p>Visual progress bars for each skill section (Basic, Sliding, Control, Turning, Flow) with completion percentages.</p>
                </div>
                
                <div class="feature-card">
                    <h4>🔗 Contact & Social</h4>
                    <p>SnowNavi contact information, website, and QR codes for WeChat and Xiaohongshu social media channels.</p>
                </div>
                
                <div class="feature-card">
                    <h4>📱 High Quality</h4>
                    <p>High-resolution JPG format suitable for printing or digital sharing, with optimized compression for file size.</p>
                </div>
                
                <div class="feature-card">
                    <h4>🎯 Personalized</h4>
                    <p>Each certificate is unique to the student's actual progress and achievements in the snowboarding program.</p>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🎯 Certificate Contents</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>Header Section</h4>
                    <ul>
                        <li>Certificate title and subtitle</li>
                        <li>SnowNavi Snow Club branding</li>
                        <li>Professional tagline</li>
                        <li>Decorative borders</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4>Student Details</h4>
                    <ul>
                        <li>Student full name</li>
                        <li>Unique member ID</li>
                        <li>Course name and date</li>
                        <li>Participation confirmation</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4>Skill Assessment</h4>
                    <ul>
                        <li>5 skill section progress bars</li>
                        <li>Individual section completion</li>
                        <li>Overall progress percentage</li>
                        <li>Color-coded skill categories</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4>Verification & Contact</h4>
                    <ul>
                        <li>Member verification QR code</li>
                        <li>SnowNavi contact information</li>
                        <li>WeChat and Xiaohongshu QR codes</li>
                        <li>Issue date and authenticity</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🚀 How It Works</h2>
            
            <ol style="font-size: 1.1rem; line-height: 1.6;">
                <li><strong>Student Access:</strong> Students visit their feedback portal and enter their email</li>
                <li><strong>Course Selection:</strong> Choose the course for which they want a certificate</li>
                <li><strong>Certificate Generation:</strong> Click "Download Certificate" to generate personalized certificate</li>
                <li><strong>Skill Integration:</strong> Certificate automatically includes their actual skill progress</li>
                <li><strong>Download:</strong> High-quality JPG file downloads automatically</li>
                <li><strong>Sharing:</strong> Students can print, email, or share on social media</li>
            </ol>
        </div>
        
        <div class="portal-link">
            <h3 style="color: var(--main-red); margin-bottom: 1rem;">Test the Certificate Feature</h3>
            <a href="test_certificate.html" target="_blank">🧪 Certificate Generator Test</a>
            <a href="student_feedback.html" target="_blank">🎿 Student Feedback Portal</a>
            <a href="test_student_feedback.html" target="_blank" class="secondary">📚 Integration Test Guide</a>
        </div>
        
        <div class="demo-section">
            <h2>💡 Benefits</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>Student Motivation</h4>
                    <p>Tangible recognition of progress encourages continued learning and skill development.</p>
                </div>
                
                <div class="feature-card">
                    <h4>Professional Credibility</h4>
                    <p>Official SnowNavi certificates add credibility to the instruction program.</p>
                </div>
                
                <div class="feature-card">
                    <h4>Marketing Value</h4>
                    <p>Students sharing certificates on social media provides organic marketing for SnowNavi.</p>
                </div>
                
                <div class="feature-card">
                    <h4>Progress Tracking</h4>
                    <p>Visual skill progress helps students understand their development journey.</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
