<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Skill Assessment Demo - SnowNavi Feedback System</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 2rem; 
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section { 
            margin: 2rem 0; 
            padding: 1rem; 
            border: 1px solid #ddd; 
            border-radius: 6px;
        }
        .skill-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        .skill-card {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 6px;
            border-left: 4px solid #E53512;
        }
        .skill-card h4 {
            margin: 0 0 0.5rem 0;
            color: #E53512;
        }
        .skill-list {
            font-size: 0.85rem;
            line-height: 1.4;
        }
        .skill-list li {
            margin-bottom: 0.25rem;
        }
        h1 { color: #E53512; }
        h2 { color: #333; border-bottom: 2px solid #E53512; padding-bottom: 0.5rem; }
        .highlight {
            background: #fff3cd;
            padding: 1rem;
            border-radius: 4px;
            border-left: 4px solid #ffc107;
            margin: 1rem 0;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        .feature-item {
            background: #e3f2fd;
            padding: 1rem;
            border-radius: 4px;
            border-left: 4px solid #2196f3;
        }
        .feature-item h4 {
            margin: 0 0 0.5rem 0;
            color: #1976d2;
        }
        code {
            background: #f1f1f1;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎿 SnowNavi Skill Assessment System</h1>
        
        <div class="highlight">
            <strong>New Feature:</strong> Comprehensive snowboarding skill assessment with 5 progressive sections, 
            25 specific skills, individual progress tracking, and section-based feedback.
        </div>
        
        <div class="demo-section">
            <h2>📋 Skill Assessment Overview</h2>
            <p>The new feedback system includes a structured skill assessment covering the complete snowboarding learning progression:</p>
            
            <div class="skill-overview">
                <div class="skill-card">
                    <h4>Section 1: Basic 基础知识</h4>
                    <ul class="skill-list">
                        <li>滑雪装备介绍 (Equipment Introduction)</li>
                        <li>单脚熟悉雪板 (Single Foot Board Familiarity)</li>
                    </ul>
                    <strong>2 skills</strong>
                </div>
                
                <div class="skill-card">
                    <h4>Section 2: Sliding 滑行</h4>
                    <ul class="skill-list">
                        <li>单脚滑板式滑动 (Single Foot Skateboard Sliding)</li>
                        <li>单脚爬坡 (Single Foot Climbing)</li>
                        <li>单脚直滑降 (Single Foot Straight Descent)</li>
                        <li>单脚脚后跟减速 (Single Foot Heel Braking)</li>
                        <li>单脚J弯 (Single Foot J-Turn)</li>
                    </ul>
                    <strong>5 skills</strong>
                </div>
                
                <div class="skill-card">
                    <h4>Section 3: Control 控制</h4>
                    <ul class="skill-list">
                        <li>静态踩油门练习 (Static Gas Pedal Practice)</li>
                        <li>单脚后刃推坡 (Single Foot Heel Side Push)</li>
                        <li>单脚前刃推坡 (Single Foot Toe Side Push)</li>
                        <li>双脚后刃推坡 (Both Feet Heel Side Push)</li>
                        <li>双脚前刃推坡 (Both Feet Toe Side Push)</li>
                        <li>双脚后刃落叶飘 (Both Feet Heel Side Falling Leaf)</li>
                        <li>双脚前刃落叶飘 (Both Feet Toe Side Falling Leaf)</li>
                        <li>双脚后刃强力落叶飘 (Both Feet Heel Side Power Falling Leaf)</li>
                        <li>双脚前刃强力落叶飘 (Both Feet Toe Side Power Falling Leaf)</li>
                    </ul>
                    <strong>9 skills</strong>
                </div>
                
                <div class="skill-card">
                    <h4>Section 4: Turning 转弯</h4>
                    <ul class="skill-list">
                        <li>静态旋转练习 (Static Rotation Practice)</li>
                        <li>阶梯转弯 (Step Turns)</li>
                        <li>J弯 (J-Turns)</li>
                        <li>走步模拟换刃 (Walking Edge Change Simulation)</li>
                        <li>新手转弯 (Beginner Turns)</li>
                    </ul>
                    <strong>5 skills</strong>
                </div>
                
                <div class="skill-card">
                    <h4>Section 5: Flow 流畅性</h4>
                    <ul class="skill-list">
                        <li>换刃后增加横穿雪道 (Edge Change with Slope Traverse)</li>
                        <li>横穿雪道加入身体起伏 (Traverse with Body Movement)</li>
                        <li>连续换刃 (Continuous Edge Changes)</li>
                        <li>搓雪360 (Scrub 360)</li>
                    </ul>
                    <strong>4 skills</strong>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>✨ Key Features</h2>
            
            <div class="feature-list">
                <div class="feature-item">
                    <h4>📊 Progress Tracking</h4>
                    <p>Real-time progress bars show completion status for each section (e.g., "3/5 completed")</p>
                </div>
                
                <div class="feature-item">
                    <h4>🎯 Visual Feedback</h4>
                    <p>Completed skills are highlighted in green, and fully completed sections turn green</p>
                </div>
                
                <div class="feature-item">
                    <h4>📝 Section-Based Feedback</h4>
                    <p>Each section has its own text area for specific feedback on that skill category</p>
                </div>
                
                <div class="feature-item">
                    <h4>💾 Data Persistence</h4>
                    <p>All skill assessments and feedback are saved and can be edited later</p>
                </div>
                
                <div class="feature-item">
                    <h4>📱 Mobile Optimized</h4>
                    <p>Responsive design works perfectly on phones and tablets</p>
                </div>
                
                <div class="feature-item">
                    <h4>🔄 Backward Compatible</h4>
                    <p>Existing feedback data continues to work with the new system</p>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🔧 Technical Implementation</h2>
            
            <h3>Data Structure</h3>
            <p>The skill assessment data is stored as part of the feedback record:</p>
            <pre><code>{
  "overallFeedback": "Great progress today!",
  "skillAssessment": {
    "completedSkills": [
      "equipment-intro",
      "single-foot-familiarity",
      "single-foot-sliding"
    ],
    "sectionFeedbacks": {
      "basic": "Excellent understanding of equipment",
      "sliding": "Good progress on single foot techniques"
    },
    "lastUpdated": "2025-01-XX..."
  }
}</code></pre>
            
            <h3>User Interface</h3>
            <ul>
                <li><strong>Checkboxes:</strong> Each skill can be marked as completed</li>
                <li><strong>Progress Bars:</strong> Visual indication of section completion</li>
                <li><strong>Section Feedback:</strong> Individual text areas for each section</li>
                <li><strong>Overall Feedback:</strong> General comments about the session</li>
            </ul>
            
            <h3>Validation</h3>
            <ul>
                <li>At least one form of feedback (skills or text) must be provided</li>
                <li>Data is validated both on frontend and backend</li>
                <li>Graceful handling of missing or incomplete data</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <h2>🚀 How to Use</h2>
            <ol>
                <li><strong>Access:</strong> Go to <code>checkin_admin.html</code> and click "💬 Add Feedback" on any check-in record</li>
                <li><strong>Assess Skills:</strong> Check off completed skills in each section</li>
                <li><strong>Section Feedback:</strong> Add specific feedback for each skill category</li>
                <li><strong>Overall Feedback:</strong> Add general comments about the session</li>
                <li><strong>Save:</strong> All data is automatically saved and can be edited later</li>
            </ol>
            
            <div class="highlight">
                <strong>Pro Tip:</strong> Sections automatically turn green when all skills are completed, 
                making it easy to see student progress at a glance!
            </div>
        </div>
    </div>
</body>
</html>
