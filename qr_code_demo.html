<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Integration Demo - SnowNavi</title>
    <style>
        :root {
            --main-red: #E53512;
            --bg-light: #F9F4F3;
            --text-dark: #2F2F2F;
            --text-gray: #717171;
            --contrast-white: #FFFFFF;
            --accent-blue: #9ED4E7;
        }

        body { 
            font-family: 'Noto Sans SC', sans-serif; 
            margin: 2rem; 
            background: var(--bg-light);
            color: var(--text-dark);
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: var(--contrast-white);
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 3px solid var(--main-red);
        }
        h1 { color: var(--main-red); }
        h2 { color: var(--text-dark); border-bottom: 2px solid var(--main-red); padding-bottom: 0.5rem; }
        .demo-section { 
            margin: 2rem 0; 
            padding: 1.5rem; 
            border: 1px solid rgba(229, 53, 18, 0.1);
            border-radius: 8px;
            background: var(--bg-light);
        }
        .qr-comparison {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        .qr-demo {
            background: var(--contrast-white);
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
            border: 1px solid rgba(229, 53, 18, 0.2);
        }
        .qr-demo h4 {
            color: var(--main-red);
            margin-bottom: 1rem;
        }
        .qr-container {
            width: 150px;
            height: 150px;
            margin: 1rem auto;
            border: 2px solid #ddd;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
        }
        .qr-container img {
            max-width: 100%;
            max-height: 100%;
            border-radius: 4px;
        }
        .qr-container canvas {
            max-width: 100%;
            max-height: 100%;
        }
        .qr-info {
            background: var(--bg-light);
            padding: 1rem;
            border-radius: 6px;
            margin-top: 1rem;
            font-size: 0.9rem;
            text-align: left;
        }
        .qr-info strong {
            color: var(--main-red);
        }
        .test-controls {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin: 1rem 0;
            flex-wrap: wrap;
        }
        .btn {
            background: var(--main-red);
            color: var(--contrast-white);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .btn:hover {
            background: #c42e0f;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(229, 53, 18, 0.3);
        }
        .btn:disabled {
            background: var(--text-gray);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .btn-secondary {
            background: var(--accent-blue);
            color: var(--text-dark);
        }
        .btn-secondary:hover {
            background: #7bc3d4;
        }
        .status-message {
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
            text-align: center;
            font-weight: bold;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .url-display {
            background: #f8f9fa;
            padding: 0.75rem;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.9rem;
            word-break: break-all;
            border: 1px solid #dee2e6;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        .feature-card {
            background: var(--contrast-white);
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid var(--main-red);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }
        .feature-card h4 {
            color: var(--main-red);
            margin: 0 0 0.5rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 QR Code Integration Demo</h1>
            <p>Real member QR codes using the same system as member.html</p>
        </div>
        
        <div class="demo-section">
            <h2>🔍 QR Code Generation Comparison</h2>
            
            <div class="qr-comparison">
                <div class="qr-demo">
                    <h4>Real Member QR Code</h4>
                    <div class="qr-container" id="real-qr-container">
                        <div style="color: var(--text-gray);">Click "Generate" to create</div>
                    </div>
                    <div class="qr-info">
                        <strong>Method:</strong> QR Server API<br>
                        <strong>URL:</strong> <span id="member-url">member.html?id=**********</span><br>
                        <strong>Same as:</strong> member.html page<br>
                        <strong>Scannable:</strong> Yes, links to member page
                    </div>
                </div>
                
                <div class="qr-demo">
                    <h4>Fallback Pattern</h4>
                    <div class="qr-container" id="fallback-qr-container">
                        <div style="color: var(--text-gray);">Click "Generate" to create</div>
                    </div>
                    <div class="qr-info">
                        <strong>Method:</strong> Canvas pattern<br>
                        <strong>Content:</strong> Visual pattern only<br>
                        <strong>Used when:</strong> API fails<br>
                        <strong>Scannable:</strong> No, decorative only
                    </div>
                </div>
            </div>
            
            <div class="test-controls">
                <input type="text" id="member-id-input" value="**********" placeholder="Enter Member ID" style="padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; margin-right: 1rem;">
                <button class="btn" onclick="generateQRCodes()">🔄 Generate QR Codes</button>
                <button class="btn btn-secondary" onclick="testMemberPage()">🔗 Test Member Page</button>
            </div>
            
            <div id="status-message"></div>
        </div>
        
        <div class="demo-section">
            <h2>📋 QR Code Implementation Details</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>Real QR Code Generation</h4>
                    <p><strong>API:</strong> https://api.qrserver.com/v1/create-qr-code/</p>
                    <p><strong>Size:</strong> 120x120 pixels for certificate</p>
                    <p><strong>Content:</strong> Full member page URL</p>
                    <p><strong>Format:</strong> PNG image</p>
                </div>
                
                <div class="feature-card">
                    <h4>Member URL Structure</h4>
                    <p><strong>Base:</strong> {origin}/member.html</p>
                    <p><strong>Parameter:</strong> ?id={memberID}</p>
                    <p><strong>Example:</strong> member.html?id=**********</p>
                    <p><strong>Encoding:</strong> URL encoded for QR generation</p>
                </div>
                
                <div class="feature-card">
                    <h4>Error Handling</h4>
                    <p><strong>Primary:</strong> QR Server API</p>
                    <p><strong>Fallback:</strong> Canvas pattern</p>
                    <p><strong>Timeout:</strong> 5 second API timeout</p>
                    <p><strong>Retry:</strong> Automatic fallback on failure</p>
                </div>
                
                <div class="feature-card">
                    <h4>Certificate Integration</h4>
                    <p><strong>Position:</strong> Center of certificate</p>
                    <p><strong>Size:</strong> 120x120 pixels</p>
                    <p><strong>Label:</strong> "Scan to verify member"</p>
                    <p><strong>Context:</strong> Member ID displayed below</p>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🧪 Testing Instructions</h2>
            
            <ol style="line-height: 1.8;">
                <li><strong>Generate QR Codes:</strong> Click the "Generate QR Codes" button to create both real and fallback QR codes</li>
                <li><strong>Test Different Member IDs:</strong> Change the member ID and regenerate to see unique QR codes</li>
                <li><strong>Scan Real QR Code:</strong> Use a QR scanner app to scan the real QR code - it should open the member page</li>
                <li><strong>Test Member Page:</strong> Click "Test Member Page" to verify the URL works correctly</li>
                <li><strong>Check Certificate:</strong> Generate a certificate to see the QR code in context</li>
            </ol>
            
            <div style="background: var(--bg-light); padding: 1rem; border-radius: 6px; margin: 1rem 0;">
                <h4 style="color: var(--main-red); margin: 0 0 0.5rem 0;">Current Member URL:</h4>
                <div class="url-display" id="current-url">
                    {origin}/member.html?id=**********
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin: 2rem 0;">
            <h3 style="color: var(--main-red);">Test in Certificate System</h3>
            <div class="test-controls">
                <a href="test_certificate.html" target="_blank" class="btn">🧪 Certificate Generator</a>
                <a href="student_feedback.html" target="_blank" class="btn btn-secondary">🎿 Student Portal</a>
                <a href="certificate_final_test.html" target="_blank" class="btn btn-secondary">🔍 System Test</a>
            </div>
        </div>
    </div>

    <script src="certificate-generator.js"></script>
    <script>
        let certificateGenerator = new CertificateGenerator();
        
        function updateCurrentUrl() {
            const memberId = document.getElementById('member-id-input').value;
            const memberUrl = `${window.location.origin}/member.html?id=${memberId}`;
            document.getElementById('member-url').textContent = `member.html?id=${memberId}`;
            document.getElementById('current-url').textContent = memberUrl;
        }
        
        async function generateQRCodes() {
            const memberId = document.getElementById('member-id-input').value;
            const statusDiv = document.getElementById('status-message');
            
            if (!memberId) {
                statusDiv.innerHTML = '<div class="status-error">Please enter a member ID</div>';
                return;
            }
            
            statusDiv.innerHTML = '<div class="status-info">🔄 Generating QR codes...</div>';
            
            try {
                // Update URL display
                updateCurrentUrl();
                
                // Generate real QR code
                const realQRContainer = document.getElementById('real-qr-container');
                realQRContainer.innerHTML = '<div style="color: var(--text-gray);">Loading...</div>';
                
                const realQR = await certificateGenerator.generateMemberQRCode(memberId, 150);
                if (realQR) {
                    realQRContainer.innerHTML = '';
                    realQRContainer.appendChild(realQR);
                } else {
                    realQRContainer.innerHTML = '<div style="color: red;">Failed to load</div>';
                }
                
                // Generate fallback pattern
                const fallbackQRContainer = document.getElementById('fallback-qr-container');
                const fallbackQR = certificateGenerator.generateMemberQRPattern(memberId, 150);
                fallbackQRContainer.innerHTML = '';
                fallbackQRContainer.appendChild(fallbackQR);
                
                statusDiv.innerHTML = '<div class="status-success">✅ QR codes generated successfully!</div>';
                
            } catch (error) {
                console.error('Error generating QR codes:', error);
                statusDiv.innerHTML = `<div class="status-error">❌ Error: ${error.message}</div>`;
            }
        }
        
        function testMemberPage() {
            const memberId = document.getElementById('member-id-input').value;
            const memberUrl = `${window.location.origin}/member.html?id=${memberId}`;
            window.open(memberUrl, '_blank');
        }
        
        // Initialize with default member ID
        window.onload = () => {
            updateCurrentUrl();
            setTimeout(generateQRCodes, 500);
        };
        
        // Update URL when member ID changes
        document.getElementById('member-id-input').addEventListener('input', updateCurrentUrl);
    </script>
</body>
</html>
