<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multilingual Certificate Test - SnowNavi</title>
    <style>
        :root {
            --main-red: #E53512;
            --bg-light: #F9F4F3;
            --text-dark: #2F2F2F;
            --text-gray: #717171;
            --contrast-white: #FFFFFF;
        }

        body { 
            font-family: 'Noto Sans SC', sans-serif; 
            margin: 2rem; 
            background: var(--bg-light);
            color: var(--text-dark);
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: var(--contrast-white);
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 3px solid var(--main-red);
        }
        h1 { color: var(--main-red); }
        .test-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border: 2px solid var(--main-red);
            border-radius: 8px;
        }
        .btn {
            background: var(--main-red);
            color: var(--contrast-white);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 0.5rem;
        }
        .btn:hover {
            background: #c42e0f;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(229, 53, 18, 0.3);
        }
        .preview-canvas {
            border: 2px solid var(--main-red);
            border-radius: 8px;
            max-width: 100%;
            height: auto;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            margin: 1rem 0;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin: 2rem 0;
        }
        .language-section {
            text-align: center;
        }
        .language-section h3 {
            color: var(--main-red);
            margin-bottom: 1rem;
        }
        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 Multilingual Certificate Test</h1>
            <p>Testing English and Chinese certificate generation</p>
        </div>
        
        <div class="test-section">
            <h2>🎯 Test Certificate Generation</h2>
            <p>Click the buttons below to generate and compare certificates in different languages:</p>
            
            <div style="text-align: center; margin: 2rem 0;">
                <button class="btn" onclick="generateEnglishCertificate()">🇺🇸 Generate English Certificate</button>
                <button class="btn" onclick="generateChineseCertificate()">🇨🇳 Generate Chinese Certificate</button>
                <button class="btn" onclick="generateBothCertificates()">🌐 Generate Both for Comparison</button>
            </div>
        </div>

        <div class="comparison">
            <div class="language-section">
                <h3>English Certificate</h3>
                <canvas id="english-canvas" class="preview-canvas" style="display: none;"></canvas>
                <div id="english-placeholder" style="padding: 2rem; color: var(--text-gray); border: 2px dashed #ddd; border-radius: 8px;">
                    Click "Generate English Certificate" to see preview
                </div>
            </div>
            
            <div class="language-section">
                <h3>Chinese Certificate 中文证书</h3>
                <canvas id="chinese-canvas" class="preview-canvas" style="display: none;"></canvas>
                <div id="chinese-placeholder" style="padding: 2rem; color: var(--text-gray); border: 2px dashed #ddd; border-radius: 8px;">
                    Click "Generate Chinese Certificate" to see preview
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>📋 Test Data</h2>
            <p><strong>Student Name:</strong> 张伟 (Zhang Wei)</p>
            <p><strong>Member ID:</strong> SN20250001</p>
            <p><strong>Course:</strong> Advanced Snowboard Lesson</p>
            <p><strong>Date:</strong> January 27, 2025</p>
            <p><strong>Skills:</strong> 20/25 completed (Advanced level)</p>
        </div>
    </div>

    <script src="certificate-generator.js"></script>
    <script>
        let certificateGenerator = new CertificateGenerator();
        
        // Test data
        const testData = {
            memberName: '张伟',
            memberId: 'SN20250001',
            activityName: 'Advanced Snowboard Lesson',
            activityDate: 'January 27, 2025',
            feedback: {
                skillAssessment: {
                    completedSkills: [
                        'equipment-intro', 'single-foot-familiarity', 'single-foot-sliding', 
                        'single-foot-climbing', 'single-foot-straight', 'single-foot-heel-brake', 
                        'single-foot-j-turn', 'static-gas-pedal', 'single-heel-side-push', 
                        'single-toe-side-push', 'both-heel-side-push', 'both-toe-side-push', 
                        'both-heel-falling-leaf', 'both-toe-falling-leaf', 'both-heel-power-falling-leaf', 
                        'both-toe-power-falling-leaf', 'static-rotation', 'step-turns', 
                        'j-turns', 'walking-edge-change'
                    ],
                    sectionFeedbacks: {
                        basic: "Excellent foundation skills",
                        sliding: "Great progress on movement",
                        control: "Good edge control development",
                        turning: "Turning technique improving",
                        flow: "Advanced flow techniques mastered"
                    }
                }
            }
        };

        async function generateEnglishCertificate() {
            try {
                const placeholder = document.getElementById('english-placeholder');
                placeholder.innerHTML = '<div style="padding: 2rem; color: var(--text-gray);">🔄 Generating English certificate...</div>';
                
                const canvas = await certificateGenerator.generateAdvancedCertificate(testData, 'en');
                
                const previewCanvas = document.getElementById('english-canvas');
                previewCanvas.width = canvas.width;
                previewCanvas.height = canvas.height;
                const previewCtx = previewCanvas.getContext('2d');
                previewCtx.drawImage(canvas, 0, 0);
                
                previewCanvas.style.display = 'block';
                placeholder.style.display = 'none';
                
            } catch (error) {
                console.error('Error generating English certificate:', error);
                document.getElementById('english-placeholder').innerHTML = 
                    '<div style="padding: 2rem; color: red;">❌ Error generating certificate</div>';
            }
        }

        async function generateChineseCertificate() {
            try {
                const placeholder = document.getElementById('chinese-placeholder');
                placeholder.innerHTML = '<div style="padding: 2rem; color: var(--text-gray);">🔄 Generating Chinese certificate...</div>';
                
                const canvas = await certificateGenerator.generateAdvancedCertificate(testData, 'zh');
                
                const previewCanvas = document.getElementById('chinese-canvas');
                previewCanvas.width = canvas.width;
                previewCanvas.height = canvas.height;
                const previewCtx = previewCanvas.getContext('2d');
                previewCtx.drawImage(canvas, 0, 0);
                
                previewCanvas.style.display = 'block';
                placeholder.style.display = 'none';
                
            } catch (error) {
                console.error('Error generating Chinese certificate:', error);
                document.getElementById('chinese-placeholder').innerHTML = 
                    '<div style="padding: 2rem; color: red;">❌ Error generating certificate</div>';
            }
        }

        async function generateBothCertificates() {
            await Promise.all([
                generateEnglishCertificate(),
                generateChineseCertificate()
            ]);
        }
    </script>
</body>
</html>
