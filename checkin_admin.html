<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Check-in Admin Panel</title>

  <!-- Favicon -->
  <link rel="icon" type="image/png" sizes="32x32" href="assets/picture/snownavi_logo.png">
  <link rel="icon" type="image/png" sizes="16x16" href="assets/picture/snownavi_logo.png">
  <link rel="shortcut icon" href="assets/picture/snownavi_logo.png">

  <!-- QR Scanner Library -->
  <script type="module" src="https://unpkg.com/qr-scanner@1.4.2/qr-scanner.min.js"></script>

  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      background: #f7f7f7;
      height: 100vh;
      display: flex;
      flex-direction: column;
    }
    h1 {
      color: #E53512;
      padding: 1rem;
      margin: 0;
    }
    .main-container {
      display: flex;
      flex: 1;
      overflow: auto;
      gap: 1rem;
      padding: 1rem;
      min-height: 0;
    }
    .scanner-section {
      flex: 1;
      background: white;
      border-radius: 8px;
      padding: 1.5rem;
      box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
      overflow-y: auto;
      max-height: calc(100vh - 120px);
    }
    .activity-selection {
      margin-bottom: 2rem;
    }
    .activity-selection select {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-size: 1rem;
    }
    .scanner-container {
      text-align: center;
      margin-bottom: 2rem;
    }
    .scanner-instructions {
      color: #666;
      margin-bottom: 1rem;
      font-size: 0.9rem;
    }
    .camera-container {
      position: relative;
      width: 100%;
      max-width: 350px;
      margin: 0 auto 1rem auto;
      border: 2px solid #E53512;
      border-radius: 8px;
      overflow: hidden;
      background: #000;
      display: none;
    }
    .camera-container.active {
      display: block;
    }
    .camera-video {
      width: 100%;
      height: 250px;
      object-fit: cover;
    }
    .camera-overlay {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 200px;
      height: 200px;
      border: 2px solid #E53512;
      border-radius: 8px;
      pointer-events: none;
    }
    .camera-overlay::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      border: 2px solid rgba(229, 53, 18, 0.3);
      border-radius: 8px;
      animation: pulse 2s infinite;
    }
    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }
    .scanner-input-container {
      display: flex;
      gap: 0.5rem;
      margin-bottom: 1rem;
    }
    .scanner-input {
      flex: 1;
      padding: 1rem;
      font-size: 1.2rem;
      border: 2px solid #E53512;
      border-radius: 8px;
      text-align: center;
    }
    .camera-toggle-btn {
      padding: 1rem;
      border: 2px solid #E53512;
      background: white;
      color: #E53512;
      border-radius: 8px;
      cursor: pointer;
      font-size: 1.2rem;
      min-width: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .camera-toggle-btn.active {
      background: #E53512;
      color: white;
    }
    .camera-status {
      text-align: center;
      padding: 1rem;
      margin-bottom: 1rem;
      border-radius: 5px;
      display: none;
    }
    .camera-status.error {
      background: #f8d7da;
      border: 1px solid #f5c6cb;
      color: #721c24;
      display: block;
    }
    .camera-status.success {
      background: #d4edda;
      border: 1px solid #c3e6cb;
      color: #155724;
      display: block;
    }
    .member-type-selection {
      display: flex;
      gap: 1rem;
      justify-content: center;
      margin-bottom: 2rem;
    }
    .member-type-btn {
      padding: 0.75rem 1.5rem;
      border: 2px solid #E53512;
      background: white;
      color: #E53512;
      border-radius: 5px;
      cursor: pointer;
      font-size: 1rem;
    }
    .member-type-btn.active {
      background: #E53512;
      color: white;
    }
    .checkin-result {
      padding: 1rem;
      border-radius: 5px;
      margin-bottom: 1rem;
      display: none;
    }
    .checkin-success {
      background: #d4edda;
      border: 1px solid #c3e6cb;
      color: #155724;
    }
    .checkin-error {
      background: #f8d7da;
      border: 1px solid #f5c6cb;
      color: #721c24;
    }
    .checkin-history {
      flex: 1;
      background: white;
      border-radius: 8px;
      padding: 1.5rem;
      box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
      overflow-y: auto;
      max-height: calc(100vh - 120px);
    }
    .checkin-item {
      padding: 1rem;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .checkin-item:last-child {
      border-bottom: none;
    }
    .checkin-info h4 {
      margin: 0 0 0.5rem 0;
      color: #E53512;
    }
    .checkin-info p {
      margin: 0;
      color: #666;
      font-size: 0.9rem;
    }
    .member-type-badge {
      padding: 0.2rem 0.5rem;
      border-radius: 3px;
      font-size: 0.8rem;
      font-weight: bold;
    }
    .type-member {
      background: #007bff;
      color: white;
    }
    .type-coach {
      background: #28a745;
      color: white;
    }
    .btn {
      background: #E53512;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      border-radius: 5px;
      cursor: pointer;
      margin: 0.5rem 0.5rem 0.5rem 0;
    }
    .btn-secondary {
      background: #666;
    }
    /* Authentication styles */
    .auth-container {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.9);
      z-index: 1000;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }
    .auth-message {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      text-align: center;
      max-width: 400px;
    }
    .auth-message h2 {
      color: #E53512;
      margin-top: 0;
    }
    .auth-btn {
      background: #E53512;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      border-radius: 5px;
      cursor: pointer;
      margin-top: 1rem;
      text-decoration: none;
      display: inline-block;
    }
    .user-info {
      display: flex;
      align-items: center;
      position: absolute;
      top: 1rem;
      right: 1rem;
    }
    .user-info img {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      margin-right: 0.5rem;
    }
    .logout-btn {
      background: none;
      border: none;
      color: #E53512;
      cursor: pointer;
      margin-left: 1rem;
      text-decoration: underline;
    }
    .stats-section {
      display: flex;
      gap: 1rem;
      margin-bottom: 2rem;
    }
    .stat-card {
      flex: 1;
      background: #f8f9fa;
      padding: 1rem;
      border-radius: 5px;
      text-align: center;
    }
    .stat-number {
      font-size: 2rem;
      font-weight: bold;
      color: #E53512;
    }
    .stat-label {
      color: #666;
      font-size: 0.9rem;
    }
  </style>
</head>
<body>
  <!-- Authentication overlay -->
  <div class="auth-container" id="auth-container">
    <div class="auth-message">
      <h2>Authentication Required</h2>
      <p>You need to be logged in to access this page.</p>
      <a href="login.html" class="auth-btn">Go to Login</a>
    </div>
  </div>

  <div class="user-info" id="user-info"></div>

  <a href="admin.html" class="back-link" style="display: inline-block; margin: 1rem; color: #E53512; text-decoration: none;">← Back to Admin Panel</a>

  <h1>SnowNavi Check-in Management</h1>

  <div class="main-container">
    <div class="scanner-section">
      <h2>QR Code Scanner</h2>

      <div class="activity-selection">
        <label for="activity-select"><strong>Select Activity:</strong></label>
        <select id="activity-select">
          <option value="">Please select an activity</option>
        </select>
      </div>

      <div class="member-type-selection">
        <button class="member-type-btn active" data-type="member" onclick="selectMemberType('member')">Member</button>
        <button class="member-type-btn" data-type="coach" onclick="selectMemberType('coach')">Coach</button>
      </div>

      <div class="scanner-input-container">
        <input type="text" id="scanner-input" class="scanner-input" placeholder="Enter Member ID (e.g., SN20210001)" autofocus>
        <button id="camera-toggle" class="camera-toggle-btn" onclick="toggleCamera()">📷</button>
      </div>

      <div id="camera-status" class="camera-status"></div>

      <div class="scanner-container">
        <!-- Camera Scanner -->
        <div id="camera-scanner" class="camera-container">
          <video id="camera-video" class="camera-video" playsinline></video>
          <div class="camera-overlay"></div>
        </div>

        <div class="scanner-instructions" id="scanner-instructions">
          Enter Member ID manually or click the camera button to scan QR code
        </div>
      </div>

      <div id="checkin-result" class="checkin-result"></div>

      <div class="stats-section" id="stats-section">
        <!-- Stats will be loaded here -->
      </div>
    </div>

    <div class="checkin-history">
      <h2 id="checkin-history-title">Recent Check-ins</h2>
      <div id="checkin-list">
        <!-- Check-in history will be loaded here -->
      </div>
    </div>
  </div>

  <script type="module">
    import QrScanner from 'https://unpkg.com/qr-scanner@1.4.2/qr-scanner.min.js';

    let activities = {};
    let checkins = {};
    let members = {};
    let selectedActivity = null;
    let selectedMemberType = 'member';
    let qrScanner = null;
    let cameraActive = false;

    // Fetch configuration and check authentication
    async function checkAuth() {
      try {
        const response = await fetch('/api/config');
        if (!response.ok) {
          throw new Error('Failed to fetch configuration');
        }

        const config = await response.json();
        const authorizedEmail = config.authorizedEmail;

        const auth = JSON.parse(localStorage.getItem('snownavi_auth') || '{}');
        const authContainer = document.getElementById('auth-container');
        const userInfoContainer = document.getElementById('user-info');

        if (auth.email === authorizedEmail && auth.expiresAt && auth.expiresAt > Date.now()) {
          authContainer.style.display = 'none';
          userInfoContainer.innerHTML = `
            <img src="${auth.picture}" alt="Profile">
            <span>${auth.name}</span>
            <button class="logout-btn" onclick="logout()">Logout</button>
          `;
          loadData();
        } else {
          authContainer.style.display = 'flex';
          userInfoContainer.innerHTML = '';
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
        document.getElementById('auth-container').style.display = 'flex';
        document.querySelector('.auth-message p').textContent = 'Error loading configuration. Please try again later.';
      }
    }

    window.logout = function() {
      localStorage.removeItem('snownavi_auth');
      window.location.href = 'login.html';
    };

    async function loadData() {
      try {
        // Load activities
        const activitiesRes = await fetch('data/activities.json');
        activities = await activitiesRes.json();

        // Load checkins
        const checkinsRes = await fetch('data/checkins.json');
        checkins = await checkinsRes.json();

        // Load members
        const membersRes = await fetch('data/members.json');
        members = await membersRes.json();

        populateActivitySelect();
        renderCheckinHistory();
        updateStats();
        setupScanner();
      } catch (error) {
        console.error('Error loading data:', error);
        alert('Failed to load data. Please try again later.');
      }
    }

    function populateActivitySelect() {
      const activitySelect = document.getElementById('activity-select');
      activitySelect.innerHTML = '<option value="">Please select an activity</option>';

      // Filter active activities
      const activeActivities = Object.keys(activities).filter(id =>
        activities[id].status === 'active'
      );

      activeActivities.forEach(activityId => {
        const activity = activities[activityId];
        const option = document.createElement('option');
        option.value = activityId;
        option.textContent = `${activity.name.en} - ${new Date(activity.date).toLocaleDateString()}`;
        activitySelect.appendChild(option);
      });

      activitySelect.onchange = function() {
        selectedActivity = this.value;
        updateStats();
        renderCheckinHistory(); // Update check-in history when activity changes
      };
    }

    window.selectMemberType = function(type) {
      selectedMemberType = type;
      document.querySelectorAll('.member-type-btn').forEach(btn => {
        btn.classList.remove('active');
      });
      document.querySelector(`[data-type="${type}"]`).classList.add('active');
    };

    window.toggleCamera = function() {
      if (cameraActive) {
        stopCamera();
      } else {
        startCamera();
      }
    };

    async function startCamera() {
      try {
        showCameraStatus('Initializing camera...', 'success');

        const videoElement = document.getElementById('camera-video');
        const cameraContainer = document.getElementById('camera-scanner');
        const cameraToggle = document.getElementById('camera-toggle');
        const instructions = document.getElementById('scanner-instructions');

        // Check if QrScanner is supported
        if (!QrScanner.hasCamera()) {
          throw new Error('No camera found on this device');
        }

        // Create QR scanner instance
        qrScanner = new QrScanner(
          videoElement,
          result => {
            console.log('QR Code detected:', result.data);
            processCheckin(result.data);
          },
          {
            onDecodeError: error => {
              // Silently ignore decode errors (normal when no QR code is visible)
              console.log('Decode error (normal):', error);
            },
            highlightScanRegion: true,
            highlightCodeOutline: true,
          }
        );

        await qrScanner.start();

        // Update UI
        cameraContainer.classList.add('active');
        cameraToggle.classList.add('active');
        cameraToggle.textContent = '❌';
        instructions.textContent = 'Point your camera at the member\'s QR code to scan automatically';
        cameraActive = true;

        showCameraStatus('Camera ready - point at QR code to scan', 'success');

        // Hide status after 3 seconds
        setTimeout(() => {
          hideCameraStatus();
        }, 3000);

      } catch (error) {
        console.error('Camera initialization error:', error);
        let errorMessage = 'Failed to access camera. ';

        if (error.name === 'NotAllowedError') {
          errorMessage += 'Please allow camera access and try again.';
        } else if (error.name === 'NotFoundError') {
          errorMessage += 'No camera found on this device.';
        } else if (error.name === 'NotSupportedError') {
          errorMessage += 'Camera not supported in this browser.';
        } else {
          errorMessage += error.message || 'Unknown error occurred.';
        }

        showCameraStatus(errorMessage, 'error');
      }
    }

    function stopCamera() {
      if (qrScanner) {
        qrScanner.stop();
        qrScanner.destroy();
        qrScanner = null;
      }

      // Update UI
      const cameraContainer = document.getElementById('camera-scanner');
      const cameraToggle = document.getElementById('camera-toggle');
      const instructions = document.getElementById('scanner-instructions');

      cameraContainer.classList.remove('active');
      cameraToggle.classList.remove('active');
      cameraToggle.textContent = '📷';
      instructions.textContent = 'Enter Member ID manually or click the camera button to scan QR code';
      cameraActive = false;

      hideCameraStatus();
    }



    function showCameraStatus(message, type) {
      const statusElement = document.getElementById('camera-status');
      statusElement.textContent = message;
      statusElement.className = `camera-status ${type}`;
    }

    function hideCameraStatus() {
      const statusElement = document.getElementById('camera-status');
      statusElement.style.display = 'none';
    }

    function setupScanner() {
      const scannerInput = document.getElementById('scanner-input');

      // Setup manual input
      scannerInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          processCheckin(this.value.trim());
          this.value = '';
        }
      });

      // Auto-focus the input
      scannerInput.focus();
    }

    function extractMemberIdFromUrl(input) {
      // Check if input is a URL containing member ID
      if (input.includes('member.html')) {
        const urlParams = new URLSearchParams(input.split('?')[1]);
        return urlParams.get('id');
      }
      // Check if input looks like a member ID
      if (/^SN\d{4}\d{4}$/.test(input)) {
        return input;
      }
      return null;
    }

    async function processCheckin(input) {
      if (!selectedActivity) {
        showResult('Please select an activity first.', 'error');
        return;
      }

      const memberId = extractMemberIdFromUrl(input);
      if (!memberId) {
        showResult('Invalid QR code or Member ID format.', 'error');
        return;
      }

      // Check if member exists
      if (!members[memberId]) {
        showResult(`Member ${memberId} not found.`, 'error');
        return;
      }

      const member = members[memberId];

      // Check if member is active
      if (!member.isActive) {
        showResult(`Member ${member.name} has expired membership.`, 'error');
        return;
      }

      // Check if already checked in for this activity
      const existingCheckin = Object.values(checkins).find(checkin =>
        checkin.activityId === selectedActivity &&
        checkin.memberId === memberId
      );

      if (existingCheckin) {
        showResult(`${member.name} is already checked in for this activity.`, 'error');
        return;
      }

      // Create new checkin
      const checkinId = generateCheckinId();
      const newCheckin = {
        id: checkinId,
        activityId: selectedActivity,
        memberId: memberId,
        memberName: member.name,
        memberType: selectedMemberType,
        checkinTime: new Date().toISOString(),
        checkinBy: 'admin',
        notes: `Checked in via QR code scan as ${selectedMemberType}`,
        createdAt: new Date().toISOString()
      };

      checkins[checkinId] = newCheckin;

      // Save checkins
      await saveCheckins();

      showResult(`✅ ${member.name} checked in successfully as ${selectedMemberType}!`, 'success');
      renderCheckinHistory();
      updateStats();
    }

    function generateCheckinId() {
      const currentYear = new Date().getFullYear();
      const prefix = `CHK${currentYear}`;

      let maxSequence = 0;
      Object.keys(checkins).forEach(id => {
        if (id.startsWith(prefix)) {
          const sequenceStr = id.substring(prefix.length);
          const sequence = parseInt(sequenceStr, 10);
          if (!isNaN(sequence) && sequence > maxSequence) {
            maxSequence = sequence;
          }
        }
      });

      const newSequence = maxSequence + 1;
      return `${prefix}${newSequence.toString().padStart(4, '0')}`;
    }

    function showResult(message, type) {
      const resultDiv = document.getElementById('checkin-result');
      resultDiv.className = `checkin-result checkin-${type}`;
      resultDiv.textContent = message;
      resultDiv.style.display = 'block';

      // Hide after 5 seconds
      setTimeout(() => {
        resultDiv.style.display = 'none';
      }, 5000);
    }

    function renderCheckinHistory() {
      const checkinList = document.getElementById('checkin-list');
      const historyTitle = document.getElementById('checkin-history-title');
      checkinList.innerHTML = '';

      // Update title based on selected activity
      if (selectedActivity) {
        const activity = activities[selectedActivity];
        const activityName = activity ? activity.name.en : 'Selected Activity';
        historyTitle.textContent = `Check-ins for ${activityName}`;
      } else {
        historyTitle.textContent = 'Recent Check-ins';
      }

      // Filter checkins by selected activity
      let filteredCheckins = Object.values(checkins);

      if (selectedActivity) {
        filteredCheckins = filteredCheckins.filter(checkin =>
          checkin.activityId === selectedActivity
        );
      }

      // Sort by check-in time (most recent first) and limit to 20
      const recentCheckins = filteredCheckins
        .sort((a, b) => new Date(b.checkinTime) - new Date(a.checkinTime))
        .slice(0, 20);

      if (recentCheckins.length === 0) {
        const noDataMessage = document.createElement('div');
        noDataMessage.style.textAlign = 'center';
        noDataMessage.style.color = '#666';
        noDataMessage.style.padding = '2rem';

        if (selectedActivity) {
          noDataMessage.textContent = 'No check-ins for this activity yet.';
        } else {
          noDataMessage.textContent = 'Please select an activity to view check-ins.';
        }

        checkinList.appendChild(noDataMessage);
        return;
      }

      recentCheckins.forEach(checkin => {
        const checkinItem = document.createElement('div');
        checkinItem.className = 'checkin-item';

        // If no activity is selected, show activity name; otherwise, just show member info
        let activityInfo = '';
        if (!selectedActivity) {
          const activity = activities[checkin.activityId];
          const activityName = activity ? activity.name.en : 'Unknown Activity';
          activityInfo = `<p><strong>Activity:</strong> ${activityName}</p>`;
        }

        checkinItem.innerHTML = `
          <div class="checkin-info">
            <h4>${checkin.memberName} <span class="member-type-badge type-${checkin.memberType}">${checkin.memberType}</span></h4>
            ${activityInfo}
            <p><strong>Time:</strong> ${new Date(checkin.checkinTime).toLocaleString()}</p>
          </div>
        `;

        checkinList.appendChild(checkinItem);
      });
    }

    function updateStats() {
      const statsSection = document.getElementById('stats-section');

      let totalCheckins = 0;
      let memberCheckins = 0;
      let coachCheckins = 0;
      let todayCheckins = 0;

      const today = new Date().toDateString();

      Object.values(checkins).forEach(checkin => {
        // Filter by selected activity if any
        if (selectedActivity && checkin.activityId !== selectedActivity) {
          return;
        }

        totalCheckins++;

        if (checkin.memberType === 'member') {
          memberCheckins++;
        } else if (checkin.memberType === 'coach') {
          coachCheckins++;
        }

        if (new Date(checkin.checkinTime).toDateString() === today) {
          todayCheckins++;
        }
      });

      statsSection.innerHTML = `
        <div class="stat-card">
          <div class="stat-number">${totalCheckins}</div>
          <div class="stat-label">Total Check-ins</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">${memberCheckins}</div>
          <div class="stat-label">Members</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">${coachCheckins}</div>
          <div class="stat-label">Coaches</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">${todayCheckins}</div>
          <div class="stat-label">Today</div>
        </div>
      `;
    }

    async function saveCheckins() {
      try {
        const response = await fetch('data/checkins.json', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(checkins, null, 2)
        });

        if (!response.ok) {
          throw new Error('Failed to save checkins');
        }
      } catch (error) {
        console.error('Error saving checkins:', error);
        throw error;
      }
    }

    // Initialize the page
    window.onload = checkAuth;

    // Clean up camera when page is unloaded
    window.addEventListener('beforeunload', () => {
      stopCamera();
    });

    // Also clean up when page becomes hidden (mobile browsers)
    document.addEventListener('visibilitychange', () => {
      if (document.hidden && qrScanner) {
        qrScanner.stop();
      } else if (!document.hidden && qrScanner && cameraActive) {
        qrScanner.start();
      }
    });
  </script>
</body>
</html>
