<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Check-in Admin Panel</title>

  <!-- Favicon -->
  <link rel="icon" type="image/png" sizes="32x32" href="assets/picture/snownavi_logo.png">
  <link rel="icon" type="image/png" sizes="16x16" href="assets/picture/snownavi_logo.png">
  <link rel="shortcut icon" href="assets/picture/snownavi_logo.png">

  <!-- QR Scanner Library -->
  <script type="module" src="https://unpkg.com/qr-scanner@1.4.2/qr-scanner.min.js"></script>

  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      background: #f7f7f7;
      height: 100vh;
      display: flex;
      flex-direction: column;
    }
    h1 {
      color: #E53512;
      padding: 1rem;
      margin: 0;
    }
    .main-container {
      display: flex;
      flex: 1;
      overflow: auto;
      gap: 1rem;
      padding: 1rem;
      min-height: 0;
    }
    .scanner-section {
      flex: 1;
      background: white;
      border-radius: 8px;
      padding: 1.5rem;
      box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
      overflow-y: auto;
      max-height: calc(100vh - 120px);
    }
    .activity-selection {
      margin-bottom: 2rem;
    }
    .activity-selection select {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-size: 1rem;
    }
    .scanner-container {
      text-align: center;
      margin-bottom: 2rem;
    }
    .scanner-instructions {
      color: #666;
      margin-bottom: 1rem;
      font-size: 0.9rem;
    }
    .camera-container {
      position: relative;
      width: 100%;
      max-width: 350px;
      margin: 0 auto 1rem auto;
      border: 2px solid #E53512;
      border-radius: 8px;
      overflow: hidden;
      background: #000;
      display: none;
    }
    .camera-container.active {
      display: block;
    }
    .camera-video {
      width: 100%;
      height: 250px;
      object-fit: cover;
    }
    .camera-overlay {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 200px;
      height: 200px;
      border: 2px solid #E53512;
      border-radius: 8px;
      pointer-events: none;
    }
    .camera-overlay::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      border: 2px solid rgba(229, 53, 18, 0.3);
      border-radius: 8px;
      animation: pulse 2s infinite;
    }
    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }
    .scanner-input-container {
      display: flex;
      gap: 0.5rem;
      margin-bottom: 1rem;
    }
    .scanner-input {
      flex: 1;
      padding: 1rem;
      font-size: 1.2rem;
      border: 2px solid #E53512;
      border-radius: 8px;
      text-align: center;
    }
    .camera-toggle-btn {
      padding: 1rem;
      border: 2px solid #E53512;
      background: white;
      color: #E53512;
      border-radius: 8px;
      cursor: pointer;
      font-size: 1.2rem;
      min-width: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .camera-toggle-btn.active {
      background: #E53512;
      color: white;
    }
    .camera-status {
      text-align: center;
      padding: 1rem;
      margin-bottom: 1rem;
      border-radius: 5px;
      display: none;
    }
    .camera-status.error {
      background: #f8d7da;
      border: 1px solid #f5c6cb;
      color: #721c24;
      display: block;
    }
    .camera-status.success {
      background: #d4edda;
      border: 1px solid #c3e6cb;
      color: #155724;
      display: block;
    }
    .member-type-selection {
      display: flex;
      gap: 1rem;
      justify-content: center;
      margin-bottom: 2rem;
    }
    .member-type-btn {
      padding: 0.75rem 1.5rem;
      border: 2px solid #E53512;
      background: white;
      color: #E53512;
      border-radius: 5px;
      cursor: pointer;
      font-size: 1rem;
    }
    .member-type-btn.active {
      background: #E53512;
      color: white;
    }
    .checkin-result {
      padding: 1rem;
      border-radius: 5px;
      margin-bottom: 1rem;
      display: none;
    }
    .checkin-success {
      background: #d4edda;
      border: 1px solid #c3e6cb;
      color: #155724;
    }
    .checkin-error {
      background: #f8d7da;
      border: 1px solid #f5c6cb;
      color: #721c24;
    }
    .checkin-warning {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      color: #856404;
    }
    .checkin-history {
      flex: 1;
      background: white;
      border-radius: 8px;
      padding: 1.5rem;
      box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
      overflow-y: auto;
      max-height: calc(100vh - 120px);
    }
    .checkin-item {
      padding: 1rem;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;
      transition: background-color 0.2s;
    }
    .checkin-item:hover {
      background-color: #f8f9fa;
    }
    .checkin-item:last-child {
      border-bottom: none;
    }
    .checkin-info h4 {
      margin: 0 0 0.5rem 0;
      color: #E53512;
    }
    .checkin-info p {
      margin: 0;
      color: #666;
      font-size: 0.9rem;
    }
    .member-type-badge {
      padding: 0.2rem 0.5rem;
      border-radius: 3px;
      font-size: 0.8rem;
      font-weight: bold;
    }
    .type-member {
      background: #007bff;
      color: white;
    }
    .type-coach {
      background: #28a745;
      color: white;
    }
    .delete-checkin-btn {
      background: #dc3545;
      color: white;
      border: none;
      padding: 0.5rem 0.75rem;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.8rem;
      transition: background-color 0.2s;
      margin-left: 1rem;
    }
    .delete-checkin-btn:hover {
      background: #c82333;
      transform: translateY(-1px);
    }
    .delete-checkin-btn:active {
      transform: translateY(0);
    }
    .checkin-actions {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      opacity: 0.7;
      transition: opacity 0.2s;
    }
    .checkin-item:hover .checkin-actions {
      opacity: 1;
    }
    .btn {
      background: #E53512;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      border-radius: 5px;
      cursor: pointer;
      margin: 0.5rem 0.5rem 0.5rem 0;
    }
    .btn-secondary {
      background: #666;
    }
    /* Authentication styles */
    .auth-container {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.9);
      z-index: 1000;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }
    .auth-message {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      text-align: center;
      max-width: 400px;
    }
    .auth-message h2 {
      color: #E53512;
      margin-top: 0;
    }
    .auth-btn {
      background: #E53512;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      border-radius: 5px;
      cursor: pointer;
      margin-top: 1rem;
      text-decoration: none;
      display: inline-block;
    }
    .user-info {
      display: flex;
      align-items: center;
      position: absolute;
      top: 1rem;
      right: 1rem;
    }
    .user-info img {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      margin-right: 0.5rem;
    }
    .logout-btn {
      background: none;
      border: none;
      color: #E53512;
      cursor: pointer;
      margin-left: 1rem;
      text-decoration: underline;
    }
    .stats-section {
      display: flex;
      gap: 1rem;
      margin-bottom: 2rem;
    }
    .stat-card {
      flex: 1;
      background: #f8f9fa;
      padding: 1rem;
      border-radius: 5px;
      text-align: center;
    }
    .stat-number {
      font-size: 2rem;
      font-weight: bold;
      color: #E53512;
    }
    .stat-label {
      color: #666;
      font-size: 0.9rem;
    }

    /* Feedback modal styles */
    .feedback-modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      overflow: auto;
    }

    .feedback-modal-content {
      background-color: #fff;
      margin: 10% auto;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      width: 90%;
      max-width: 600px;
      position: relative;
    }

    .feedback-modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid #eee;
    }

    .feedback-modal-header h2 {
      margin: 0;
      color: #E53512;
      font-size: 1.5rem;
    }

    .feedback-close {
      background: none;
      border: none;
      font-size: 2rem;
      cursor: pointer;
      color: #999;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .feedback-close:hover {
      color: #E53512;
    }

    .feedback-member-info {
      background: #f8f9fa;
      padding: 1rem;
      border-radius: 6px;
      margin-bottom: 1.5rem;
    }

    .feedback-member-info h3 {
      margin: 0 0 0.5rem 0;
      color: #E53512;
    }

    .feedback-member-info p {
      margin: 0.25rem 0;
      color: #666;
      font-size: 0.9rem;
    }

    .feedback-form {
      display: flex;
      flex-direction: column;
    }

    .feedback-form label {
      font-weight: bold;
      margin-bottom: 0.5rem;
      color: #333;
    }

    .feedback-textarea {
      width: 100%;
      min-height: 120px;
      padding: 0.75rem;
      border: 2px solid #ddd;
      border-radius: 6px;
      font-family: inherit;
      font-size: 1rem;
      resize: vertical;
      box-sizing: border-box;
    }

    .feedback-textarea:focus {
      outline: none;
      border-color: #E53512;
      box-shadow: 0 0 0 3px rgba(229, 53, 18, 0.1);
    }

    .feedback-actions {
      display: flex;
      justify-content: flex-end;
      gap: 1rem;
      margin-top: 1.5rem;
    }

    .feedback-btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 6px;
      font-size: 1rem;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .feedback-btn-cancel {
      background: #6c757d;
      color: white;
    }

    .feedback-btn-cancel:hover {
      background: #5a6268;
    }

    .feedback-btn-save {
      background: #E53512;
      color: white;
    }

    .feedback-btn-save:hover {
      background: #c52e10;
    }

    .feedback-btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .feedback-checkin-btn {
      background: #17a2b8;
      color: white;
      border: none;
      padding: 0.5rem 0.75rem;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.8rem;
      transition: background-color 0.2s;
      margin-left: 0.5rem;
    }

    .feedback-checkin-btn:hover {
      background: #138496;
    }

    .delete-feedback-btn {
      background: #dc3545;
      color: white;
      border: none;
      padding: 0.5rem 0.75rem;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.8rem;
      transition: background-color 0.2s;
      margin-left: 0.5rem;
    }

    .delete-feedback-btn:hover {
      background: #c82333;
    }

    /* Skill Assessment Styles */
    .skill-sections {
      margin-bottom: 1.5rem;
    }

    .skill-section {
      border: 2px solid #ddd;
      border-radius: 8px;
      margin-bottom: 1rem;
      padding: 1rem;
      transition: border-color 0.3s, background-color 0.3s;
    }

    .skill-section.completed {
      border-color: #28a745;
      background-color: #f8fff9;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 1px solid #eee;
    }

    .section-header h3 {
      margin: 0;
      color: #E53512;
      font-size: 1.1rem;
    }

    .skill-section.completed .section-header h3 {
      color: #28a745;
    }

    .section-progress {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      min-width: 120px;
    }

    .progress-text {
      font-size: 0.8rem;
      color: #666;
      margin-bottom: 0.25rem;
    }

    .progress-bar {
      width: 100px;
      height: 6px;
      background: #e0e0e0;
      border-radius: 3px;
      overflow: hidden;
    }

    .progress-fill {
      height: 100%;
      background: #28a745;
      transition: width 0.3s ease;
    }

    .skill-items {
      margin-bottom: 1rem;
    }

    .skill-item {
      display: flex;
      align-items: center;
      margin-bottom: 0.5rem;
      padding: 0.5rem;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .skill-item:hover {
      background-color: #f5f5f5;
    }

    .skill-item.completed {
      background-color: #d4edda;
      border: 1px solid #c3e6cb;
    }

    .skill-item input[type="checkbox"] {
      margin-right: 0.75rem;
      transform: scale(1.2);
      cursor: pointer;
    }

    .skill-name {
      font-size: 0.9rem;
      line-height: 1.3;
      cursor: pointer;
    }

    .section-feedback {
      margin-top: 1rem;
      padding-top: 1rem;
      border-top: 1px solid #eee;
    }

    .section-feedback label {
      font-weight: bold;
      margin-bottom: 0.5rem;
      color: #333;
      font-size: 0.9rem;
    }

    .section-textarea {
      width: 100%;
      min-height: 60px;
      padding: 0.5rem;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-family: inherit;
      font-size: 0.85rem;
      resize: vertical;
      box-sizing: border-box;
    }

    .section-textarea:focus {
      outline: none;
      border-color: #E53512;
      box-shadow: 0 0 0 2px rgba(229, 53, 18, 0.1);
    }

    .overall-feedback {
      margin-top: 1.5rem;
      padding-top: 1.5rem;
      border-top: 2px solid #E53512;
    }

    .overall-feedback label {
      font-weight: bold;
      margin-bottom: 0.5rem;
      color: #E53512;
      font-size: 1rem;
    }

    /* Mobile responsive styles */
    @media (max-width: 768px) {
      body {
        padding: 0;
      }

      h1 {
        font-size: 1.5rem;
        text-align: center;
        margin: 0.5rem;
        padding: 0 1rem;
      }

      .back-link {
        margin: 0.5rem 1rem !important;
        font-size: 0.9rem;
      }

      .main-container {
        flex-direction: column;
        padding: 0.5rem;
        gap: 0.5rem;
        min-height: calc(100vh - 150px);
      }

      .scanner-section {
        padding: 1rem;
        max-height: none;
        flex-shrink: 0;
      }

      .scanner-section h2 {
        font-size: 1.3rem;
        margin-bottom: 1rem;
        text-align: center;
      }

      .activity-selection {
        margin-bottom: 1rem;
      }

      .activity-selection select {
        padding: 0.6rem;
        font-size: 0.9rem;
      }

      .member-type-selection {
        gap: 0.5rem;
        margin-bottom: 1rem;
      }

      .member-type-btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
        flex: 1;
      }

      .scanner-input-container {
        gap: 0.3rem;
        margin-bottom: 0.75rem;
      }

      .scanner-input {
        padding: 0.8rem;
        font-size: 1rem;
      }

      .camera-toggle-btn {
        padding: 0.8rem;
        font-size: 1rem;
        min-width: 50px;
      }

      .camera-container {
        max-width: 100%;
        margin-bottom: 0.75rem;
      }

      .camera-video {
        height: 200px;
      }

      .camera-overlay {
        width: 150px;
        height: 150px;
      }

      .scanner-instructions {
        font-size: 0.8rem;
        margin-bottom: 0.75rem;
      }

      .stats-section {
        gap: 0.5rem;
        margin-bottom: 1rem;
      }

      .stat-card {
        padding: 0.75rem;
      }

      .stat-number {
        font-size: 1.5rem;
      }

      .stat-label {
        font-size: 0.8rem;
      }

      .checkin-history {
        padding: 1rem;
        max-height: 40vh;
        min-height: 200px;
        flex: 1;
        overflow-y: auto;
      }

      .checkin-history h2 {
        font-size: 1.3rem;
        margin-bottom: 1rem;
        text-align: center;
      }

      .checkin-item {
        padding: 0.75rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
      }

      .checkin-info h4 {
        font-size: 1rem;
      }

      .checkin-info p {
        font-size: 0.8rem;
      }

      .checkin-actions {
        opacity: 1; /* Always visible on mobile */
        align-self: flex-end;
        margin-top: 0.5rem;
      }

      .delete-checkin-btn {
        padding: 0.4rem 0.6rem;
        font-size: 0.75rem;
        margin-left: 0;
      }

      .user-info {
        position: static;
        justify-content: center;
        margin: 0.5rem;
        padding: 0.5rem;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
      }

      .auth-message {
        margin: 1rem;
        padding: 1.5rem;
        max-width: calc(100% - 2rem);
      }

      /* Feedback modal mobile optimization */
      .feedback-modal-content {
        width: 95%;
        margin: 5% auto;
        padding: 1rem;
      }

      .feedback-modal-header h2 {
        font-size: 1.3rem;
      }

      .feedback-textarea {
        min-height: 100px;
        font-size: 0.9rem;
      }

      .feedback-actions {
        flex-direction: column;
        gap: 0.5rem;
      }

      .feedback-btn {
        width: 100%;
        padding: 0.8rem;
      }

      .feedback-checkin-btn {
        padding: 0.4rem 0.6rem;
        font-size: 0.75rem;
        margin-left: 0;
        margin-top: 0.5rem;
      }

      .delete-feedback-btn {
        padding: 0.4rem 0.6rem;
        font-size: 0.75rem;
        margin-left: 0;
        margin-top: 0.5rem;
      }

      /* Skill sections mobile optimization */
      .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
      }

      .section-progress {
        align-items: flex-start;
        min-width: auto;
      }

      .progress-bar {
        width: 150px;
      }

      .skill-item {
        padding: 0.75rem 0.5rem;
      }

      .skill-name {
        font-size: 0.85rem;
      }

      .section-textarea {
        min-height: 50px;
        font-size: 0.8rem;
      }
    }

    @media (max-width: 480px) {
      h1 {
        font-size: 1.3rem;
      }

      .scanner-section {
        padding: 0.75rem;
      }

      .scanner-section h2 {
        font-size: 1.2rem;
      }

      .member-type-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.85rem;
      }

      .scanner-input {
        padding: 0.7rem;
        font-size: 0.9rem;
      }

      .camera-toggle-btn {
        padding: 0.7rem;
        font-size: 0.9rem;
        min-width: 45px;
      }

      .camera-video {
        height: 180px;
      }

      .camera-overlay {
        width: 120px;
        height: 120px;
      }

      .stats-section {
        flex-wrap: wrap;
      }

      .stat-card {
        flex: 1 1 45%;
        min-width: 120px;
        padding: 0.5rem;
      }

      .stat-number {
        font-size: 1.3rem;
      }

      .stat-label {
        font-size: 0.75rem;
      }

      .checkin-history {
        padding: 0.75rem;
        max-height: 35vh;
        min-height: 150px;
      }

      .checkin-history h2 {
        font-size: 1.1rem;
      }

      .checkin-item {
        padding: 0.5rem;
      }

      .checkin-info h4 {
        font-size: 0.9rem;
      }

      .checkin-info p {
        font-size: 0.75rem;
      }

      .delete-checkin-btn {
        padding: 0.3rem 0.5rem;
        font-size: 0.7rem;
      }

      /* Feedback modal small screen optimization */
      .feedback-modal-content {
        width: 98%;
        margin: 2% auto;
        padding: 0.75rem;
      }

      .feedback-modal-header {
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
      }

      .feedback-modal-header h2 {
        font-size: 1.1rem;
      }

      .feedback-member-info {
        padding: 0.75rem;
        margin-bottom: 1rem;
      }

      .feedback-member-info h3 {
        font-size: 1rem;
      }

      .feedback-member-info p {
        font-size: 0.8rem;
      }

      .feedback-textarea {
        min-height: 80px;
        padding: 0.6rem;
        font-size: 0.85rem;
      }

      .feedback-btn {
        padding: 0.7rem;
        font-size: 0.9rem;
      }

      .feedback-checkin-btn {
        padding: 0.3rem 0.5rem;
        font-size: 0.7rem;
      }

      .delete-feedback-btn {
        padding: 0.3rem 0.5rem;
        font-size: 0.7rem;
      }

      /* Skill sections small screen optimization */
      .skill-section {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
      }

      .section-header h3 {
        font-size: 1rem;
      }

      .progress-text {
        font-size: 0.75rem;
      }

      .progress-bar {
        width: 120px;
        height: 5px;
      }

      .skill-item {
        padding: 0.6rem 0.4rem;
        margin-bottom: 0.4rem;
      }

      .skill-name {
        font-size: 0.8rem;
        line-height: 1.2;
      }

      .section-textarea {
        min-height: 45px;
        padding: 0.4rem;
        font-size: 0.75rem;
      }

      .section-feedback label {
        font-size: 0.85rem;
      }

      .overall-feedback label {
        font-size: 0.9rem;
      }
    }
  </style>
</head>
<body>
  <!-- Authentication overlay -->
  <div class="auth-container" id="auth-container">
    <div class="auth-message">
      <h2>Authentication Required</h2>
      <p>You need to be logged in to access this page.</p>
      <a href="login.html" class="auth-btn">Go to Login</a>
    </div>
  </div>

  <div class="user-info" id="user-info"></div>

  <a href="admin.html" class="back-link" style="display: inline-block; margin: 1rem; color: #E53512; text-decoration: none;">← Back to Admin Panel</a>

  <h1>SnowNavi Check-in Management</h1>

  <div class="main-container">
    <div class="scanner-section">
      <h2>QR Code Scanner</h2>

      <div class="activity-selection">
        <label for="activity-select"><strong>Select Activity:</strong></label>
        <select id="activity-select">
          <option value="">Please select an activity</option>
        </select>
      </div>

      <div class="member-type-selection">
        <button class="member-type-btn active" data-type="member" onclick="selectMemberType('member')">Member</button>
        <button class="member-type-btn" data-type="coach" onclick="selectMemberType('coach')">Coach</button>
      </div>

      <div class="scanner-input-container">
        <input type="text" id="scanner-input" class="scanner-input" placeholder="Enter Member ID (e.g., SN20210001)" autofocus>
        <button id="camera-toggle" class="camera-toggle-btn" onclick="toggleCamera()">📷</button>
      </div>

      <div id="camera-status" class="camera-status"></div>

      <div class="scanner-container">
        <!-- Camera Scanner -->
        <div id="camera-scanner" class="camera-container">
          <video id="camera-video" class="camera-video" playsinline></video>
          <div class="camera-overlay"></div>
        </div>

        <div class="scanner-instructions" id="scanner-instructions">
          Enter Member ID manually or click the camera button to scan QR code
        </div>
      </div>

      <div id="checkin-result" class="checkin-result"></div>

      <div class="stats-section" id="stats-section">
        <!-- Stats will be loaded here -->
      </div>
    </div>

    <div class="checkin-history">
      <h2 id="checkin-history-title">Recent Check-ins</h2>
      <div id="checkin-list">
        <!-- Check-in history will be loaded here -->
      </div>
    </div>
  </div>

  <!-- Feedback Modal -->
  <div id="feedback-modal" class="feedback-modal">
    <div class="feedback-modal-content">
      <div class="feedback-modal-header">
        <h2>Student Feedback</h2>
        <button class="feedback-close" onclick="closeFeedbackModal()">&times;</button>
      </div>

      <div class="feedback-member-info" id="feedback-member-info">
        <!-- Member info will be populated here -->
      </div>

      <form class="feedback-form" id="feedback-form" onsubmit="saveFeedback(event)">
        <!-- Skill Assessment Sections -->
        <div class="skill-sections">
          <!-- Section 1: Basic -->
          <div class="skill-section" data-section="basic">
            <div class="section-header">
              <h3>Section 1: Basic 基础知识</h3>
              <div class="section-progress">
                <span class="progress-text">0/2 completed</span>
                <div class="progress-bar">
                  <div class="progress-fill" style="width: 0%"></div>
                </div>
              </div>
            </div>
            <div class="skill-items">
              <label class="skill-item">
                <input type="checkbox" data-skill="equipment-intro">
                <span class="skill-name">滑雪装备介绍 (Equipment Introduction)</span>
              </label>
              <label class="skill-item">
                <input type="checkbox" data-skill="single-foot-familiarity">
                <span class="skill-name">单脚熟悉雪板 (Single Foot Board Familiarity)</span>
              </label>
            </div>
            <div class="section-feedback">
              <label for="basic-feedback">Basic Section Feedback:</label>
              <textarea
                id="basic-feedback"
                class="section-textarea"
                placeholder="Enter feedback for basic skills..."
              ></textarea>
            </div>
          </div>

          <!-- Section 2: Sliding -->
          <div class="skill-section" data-section="sliding">
            <div class="section-header">
              <h3>Section 2: Sliding 滑行</h3>
              <div class="section-progress">
                <span class="progress-text">0/5 completed</span>
                <div class="progress-bar">
                  <div class="progress-fill" style="width: 0%"></div>
                </div>
              </div>
            </div>
            <div class="skill-items">
              <label class="skill-item">
                <input type="checkbox" data-skill="single-foot-sliding">
                <span class="skill-name">单脚滑板式滑动 (Single Foot Skateboard Sliding)</span>
              </label>
              <label class="skill-item">
                <input type="checkbox" data-skill="single-foot-climbing">
                <span class="skill-name">单脚爬坡 (Single Foot Climbing)</span>
              </label>
              <label class="skill-item">
                <input type="checkbox" data-skill="single-foot-straight">
                <span class="skill-name">单脚直滑降 (Single Foot Straight Descent)</span>
              </label>
              <label class="skill-item">
                <input type="checkbox" data-skill="single-foot-heel-brake">
                <span class="skill-name">单脚脚后跟减速 (Single Foot Heel Braking)</span>
              </label>
              <label class="skill-item">
                <input type="checkbox" data-skill="single-foot-j-turn">
                <span class="skill-name">单脚J弯 (Single Foot J-Turn)</span>
              </label>
            </div>
            <div class="section-feedback">
              <label for="sliding-feedback">Sliding Section Feedback:</label>
              <textarea
                id="sliding-feedback"
                class="section-textarea"
                placeholder="Enter feedback for sliding skills..."
              ></textarea>
            </div>
          </div>

          <!-- Section 3: Control -->
          <div class="skill-section" data-section="control">
            <div class="section-header">
              <h3>Section 3: Control 控制</h3>
              <div class="section-progress">
                <span class="progress-text">0/9 completed</span>
                <div class="progress-bar">
                  <div class="progress-fill" style="width: 0%"></div>
                </div>
              </div>
            </div>
            <div class="skill-items">
              <label class="skill-item">
                <input type="checkbox" data-skill="static-gas-pedal">
                <span class="skill-name">静态踩油门练习 (Static Gas Pedal Practice)</span>
              </label>
              <label class="skill-item">
                <input type="checkbox" data-skill="single-heel-side-push">
                <span class="skill-name">单脚后刃推坡 (Single Foot Heel Side Push)</span>
              </label>
              <label class="skill-item">
                <input type="checkbox" data-skill="single-toe-side-push">
                <span class="skill-name">单脚前刃推坡 (Single Foot Toe Side Push)</span>
              </label>
              <label class="skill-item">
                <input type="checkbox" data-skill="both-heel-side-push">
                <span class="skill-name">双脚后刃推坡 (Both Feet Heel Side Push)</span>
              </label>
              <label class="skill-item">
                <input type="checkbox" data-skill="both-toe-side-push">
                <span class="skill-name">双脚前刃推坡 (Both Feet Toe Side Push)</span>
              </label>
              <label class="skill-item">
                <input type="checkbox" data-skill="both-heel-falling-leaf">
                <span class="skill-name">双脚后刃落叶飘 (Both Feet Heel Side Falling Leaf)</span>
              </label>
              <label class="skill-item">
                <input type="checkbox" data-skill="both-toe-falling-leaf">
                <span class="skill-name">双脚前刃落叶飘 (Both Feet Toe Side Falling Leaf)</span>
              </label>
              <label class="skill-item">
                <input type="checkbox" data-skill="both-heel-power-falling-leaf">
                <span class="skill-name">双脚后刃强力落叶飘 (Both Feet Heel Side Power Falling Leaf)</span>
              </label>
              <label class="skill-item">
                <input type="checkbox" data-skill="both-toe-power-falling-leaf">
                <span class="skill-name">双脚前刃强力落叶飘 (Both Feet Toe Side Power Falling Leaf)</span>
              </label>
            </div>
            <div class="section-feedback">
              <label for="control-feedback">Control Section Feedback:</label>
              <textarea
                id="control-feedback"
                class="section-textarea"
                placeholder="Enter feedback for control skills..."
              ></textarea>
            </div>
          </div>

          <!-- Section 4: Turning -->
          <div class="skill-section" data-section="turning">
            <div class="section-header">
              <h3>Section 4: Turning 转弯</h3>
              <div class="section-progress">
                <span class="progress-text">0/5 completed</span>
                <div class="progress-bar">
                  <div class="progress-fill" style="width: 0%"></div>
                </div>
              </div>
            </div>
            <div class="skill-items">
              <label class="skill-item">
                <input type="checkbox" data-skill="static-rotation">
                <span class="skill-name">静态旋转练习 (Static Rotation Practice)</span>
              </label>
              <label class="skill-item">
                <input type="checkbox" data-skill="step-turns">
                <span class="skill-name">阶梯转弯 (Step Turns)</span>
              </label>
              <label class="skill-item">
                <input type="checkbox" data-skill="j-turns">
                <span class="skill-name">J弯 (J-Turns)</span>
              </label>
              <label class="skill-item">
                <input type="checkbox" data-skill="walking-edge-change">
                <span class="skill-name">走步模拟换刃 (Walking Edge Change Simulation)</span>
              </label>
              <label class="skill-item">
                <input type="checkbox" data-skill="beginner-turns">
                <span class="skill-name">新手转弯 (Beginner Turns)</span>
              </label>
            </div>
            <div class="section-feedback">
              <label for="turning-feedback">Turning Section Feedback:</label>
              <textarea
                id="turning-feedback"
                class="section-textarea"
                placeholder="Enter feedback for turning skills..."
              ></textarea>
            </div>
          </div>

          <!-- Section 5: Flow -->
          <div class="skill-section" data-section="flow">
            <div class="section-header">
              <h3>Section 5: Flow 流畅性</h3>
              <div class="section-progress">
                <span class="progress-text">0/4 completed</span>
                <div class="progress-bar">
                  <div class="progress-fill" style="width: 0%"></div>
                </div>
              </div>
            </div>
            <div class="skill-items">
              <label class="skill-item">
                <input type="checkbox" data-skill="edge-change-traverse">
                <span class="skill-name">换刃后增加横穿雪道 (Edge Change with Slope Traverse)</span>
              </label>
              <label class="skill-item">
                <input type="checkbox" data-skill="traverse-body-movement">
                <span class="skill-name">横穿雪道加入身体起伏 (Traverse with Body Movement)</span>
              </label>
              <label class="skill-item">
                <input type="checkbox" data-skill="continuous-edge-change">
                <span class="skill-name">连续换刃 (Continuous Edge Changes)</span>
              </label>
              <label class="skill-item">
                <input type="checkbox" data-skill="scrub-360">
                <span class="skill-name">搓雪360 (Scrub 360)</span>
              </label>
            </div>
            <div class="section-feedback">
              <label for="flow-feedback">Flow Section Feedback:</label>
              <textarea
                id="flow-feedback"
                class="section-textarea"
                placeholder="Enter feedback for flow skills..."
              ></textarea>
            </div>
          </div>
        </div>

        <!-- Overall Feedback -->
        <div class="overall-feedback">
          <label for="feedback-text">Overall Course Performance Feedback:</label>
          <textarea
            id="feedback-text"
            class="feedback-textarea"
            placeholder="Enter overall feedback about the student's performance in this course session..."
          ></textarea>
        </div>

        <div class="feedback-actions">
          <button type="button" class="feedback-btn feedback-btn-cancel" onclick="closeFeedbackModal()">
            Cancel
          </button>
          <button type="submit" class="feedback-btn feedback-btn-save">
            Save Feedback
          </button>
        </div>
      </form>
    </div>
  </div>

  <script type="module">
    import QrScanner from 'https://unpkg.com/qr-scanner@1.4.2/qr-scanner.min.js';

    let activities = {};
    let checkins = {};
    let members = {};
    let feedbacks = {};
    let selectedActivity = null;
    let selectedMemberType = 'member';
    let qrScanner = null;
    let cameraActive = false;
    let currentFeedbackCheckinId = null;

    // Get current user information
    function getCurrentUser() {
      const auth = JSON.parse(localStorage.getItem('snownavi_auth') || '{}');
      return {
        name: auth.name || 'Unknown User',
        email: auth.email || '<EMAIL>',
        picture: auth.picture || ''
      };
    }

    // Skill assessment functions
    function initializeSkillAssessment() {
      // Add event listeners to all checkboxes
      document.querySelectorAll('.skill-item input[type="checkbox"]').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
          updateSkillItemStatus(this);
          updateSectionProgress(this.closest('.skill-section'));
        });
      });
    }

    function updateSkillItemStatus(checkbox) {
      const skillItem = checkbox.closest('.skill-item');
      if (checkbox.checked) {
        skillItem.classList.add('completed');
      } else {
        skillItem.classList.remove('completed');
      }
    }

    function updateSectionProgress(section) {
      const checkboxes = section.querySelectorAll('.skill-item input[type="checkbox"]');
      const checkedBoxes = section.querySelectorAll('.skill-item input[type="checkbox"]:checked');
      const total = checkboxes.length;
      const completed = checkedBoxes.length;
      const percentage = total > 0 ? (completed / total) * 100 : 0;

      // Update progress text
      const progressText = section.querySelector('.progress-text');
      progressText.textContent = `${completed}/${total} completed`;

      // Update progress bar
      const progressFill = section.querySelector('.progress-fill');
      progressFill.style.width = `${percentage}%`;

      // Update section completion status
      if (completed === total && total > 0) {
        section.classList.add('completed');
      } else {
        section.classList.remove('completed');
      }
    }

    function loadSkillAssessmentData(feedbackData) {
      if (!feedbackData || !feedbackData.skillAssessment) return;

      const assessment = feedbackData.skillAssessment;

      // Load completed skills
      if (assessment.completedSkills) {
        assessment.completedSkills.forEach(skillId => {
          const checkbox = document.querySelector(`input[data-skill="${skillId}"]`);
          if (checkbox) {
            checkbox.checked = true;
            updateSkillItemStatus(checkbox);
          }
        });
      }

      // Load section feedbacks
      if (assessment.sectionFeedbacks) {
        Object.keys(assessment.sectionFeedbacks).forEach(sectionId => {
          const textarea = document.getElementById(`${sectionId}-feedback`);
          if (textarea) {
            textarea.value = assessment.sectionFeedbacks[sectionId];
          }
        });
      }

      // Update all section progress
      document.querySelectorAll('.skill-section').forEach(section => {
        updateSectionProgress(section);
      });
    }

    function collectSkillAssessmentData() {
      const completedSkills = [];
      const sectionFeedbacks = {};

      // Collect completed skills
      document.querySelectorAll('.skill-item input[type="checkbox"]:checked').forEach(checkbox => {
        completedSkills.push(checkbox.getAttribute('data-skill'));
      });

      // Collect section feedbacks
      document.querySelectorAll('.section-textarea').forEach(textarea => {
        const sectionId = textarea.id.replace('-feedback', '');
        if (textarea.value.trim()) {
          sectionFeedbacks[sectionId] = textarea.value.trim();
        }
      });

      return {
        completedSkills,
        sectionFeedbacks,
        lastUpdated: new Date().toISOString()
      };
    }

    // Fetch configuration and check authentication
    async function checkAuth() {
      try {
        const response = await fetch('/api/config');
        if (!response.ok) {
          throw new Error('Failed to fetch configuration');
        }

        const config = await response.json();

        // Parse allowed emails - try new format first, fallback to old format
        let allowedEmails = [];
        if (config.allowedEmails && Array.isArray(config.allowedEmails)) {
          allowedEmails = config.allowedEmails;
        } else if (config.authorizedEmail) {
          allowedEmails = config.authorizedEmail.split(',').map(email => email.trim());
        }

        const auth = JSON.parse(localStorage.getItem('snownavi_auth') || '{}');
        const authContainer = document.getElementById('auth-container');
        const userInfoContainer = document.getElementById('user-info');

        // Check if auth exists, is not expired, and user is authorized
        const isAuthorized = allowedEmails.includes(auth.email);
        if (isAuthorized && auth.expiresAt && auth.expiresAt > Date.now()) {
          authContainer.style.display = 'none';
          userInfoContainer.innerHTML = `
            <img src="${auth.picture}" alt="Profile">
            <span>${auth.name}</span>
            <button class="logout-btn" onclick="logout()">Logout</button>
          `;
          loadData();
        } else {
          authContainer.style.display = 'flex';
          userInfoContainer.innerHTML = '';
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
        document.getElementById('auth-container').style.display = 'flex';
        document.querySelector('.auth-message p').textContent = 'Error loading configuration. Please try again later.';
      }
    }

    window.logout = function() {
      localStorage.removeItem('snownavi_auth');
      window.location.href = 'login.html';
    };

    async function loadData() {
      try {
        // Load activities
        const activitiesRes = await fetch('data/activities.json');
        activities = await activitiesRes.json();

        // Load checkins
        const checkinsRes = await fetch('data/checkins.json');
        checkins = await checkinsRes.json();

        // Load members
        const membersRes = await fetch('data/members.json');
        members = await membersRes.json();

        // Load feedbacks (create empty object if file doesn't exist)
        try {
          const feedbacksRes = await fetch('data/feedbacks.json');
          if (feedbacksRes.ok) {
            feedbacks = await feedbacksRes.json();
          } else {
            // Try to load from localStorage as fallback
            const localFeedbacks = localStorage.getItem('snownavi_feedbacks');
            if (localFeedbacks) {
              feedbacks = JSON.parse(localFeedbacks);
              console.log('Loaded feedbacks from localStorage');
            } else {
              feedbacks = {};
            }
          }
        } catch (error) {
          console.log('Feedbacks file not found, trying localStorage...');
          // Try to load from localStorage as fallback
          const localFeedbacks = localStorage.getItem('snownavi_feedbacks');
          if (localFeedbacks) {
            try {
              feedbacks = JSON.parse(localFeedbacks);
              console.log('Loaded feedbacks from localStorage');
            } catch (parseError) {
              console.error('Error parsing localStorage feedbacks:', parseError);
              feedbacks = {};
            }
          } else {
            feedbacks = {};
          }
        }

        populateActivitySelect();
        renderCheckinHistory();
        updateStats();
        setupScanner();
      } catch (error) {
        console.error('Error loading data:', error);
        alert('Failed to load data. Please try again later.');
      }
    }

    function populateActivitySelect() {
      const activitySelect = document.getElementById('activity-select');
      activitySelect.innerHTML = '<option value="">Please select an activity</option>';

      // Filter active activities
      const activeActivities = Object.keys(activities).filter(id =>
        activities[id].status === 'active'
      );

      activeActivities.forEach(activityId => {
        const activity = activities[activityId];
        const option = document.createElement('option');
        option.value = activityId;
        option.textContent = `${activity.name.en} - ${new Date(activity.date).toLocaleDateString()}`;
        activitySelect.appendChild(option);
      });

      activitySelect.onchange = function() {
        selectedActivity = this.value;
        updateStats();
        renderCheckinHistory(); // Update check-in history when activity changes
      };
    }

    window.selectMemberType = function(type) {
      selectedMemberType = type;
      document.querySelectorAll('.member-type-btn').forEach(btn => {
        btn.classList.remove('active');
      });
      document.querySelector(`[data-type="${type}"]`).classList.add('active');
    };

    window.toggleCamera = function() {
      if (cameraActive) {
        stopCamera();
      } else {
        startCamera();
      }
    };

    async function startCamera() {
      try {
        showCameraStatus('Initializing camera...', 'success');

        const videoElement = document.getElementById('camera-video');
        const cameraContainer = document.getElementById('camera-scanner');
        const cameraToggle = document.getElementById('camera-toggle');
        const instructions = document.getElementById('scanner-instructions');

        // Check if QrScanner is supported
        if (!QrScanner.hasCamera()) {
          throw new Error('No camera found on this device');
        }

        // Create QR scanner instance
        qrScanner = new QrScanner(
          videoElement,
          result => {
            console.log('QR Code detected:', result.data);
            processCheckin(result.data);
          },
          {
            onDecodeError: error => {
              // Silently ignore decode errors (normal when no QR code is visible)
              console.log('Decode error (normal):', error);
            },
            highlightScanRegion: true,
            highlightCodeOutline: true,
          }
        );

        await qrScanner.start();

        // Update UI
        cameraContainer.classList.add('active');
        cameraToggle.classList.add('active');
        cameraToggle.textContent = '❌';
        instructions.textContent = 'Point your camera at the member\'s QR code to scan automatically';
        cameraActive = true;

        showCameraStatus('Camera ready - point at QR code to scan', 'success');

        // Hide status after 3 seconds
        setTimeout(() => {
          hideCameraStatus();
        }, 3000);

      } catch (error) {
        console.error('Camera initialization error:', error);
        let errorMessage = 'Failed to access camera. ';

        if (error.name === 'NotAllowedError') {
          errorMessage += 'Please allow camera access and try again.';
        } else if (error.name === 'NotFoundError') {
          errorMessage += 'No camera found on this device.';
        } else if (error.name === 'NotSupportedError') {
          errorMessage += 'Camera not supported in this browser.';
        } else {
          errorMessage += error.message || 'Unknown error occurred.';
        }

        showCameraStatus(errorMessage, 'error');
      }
    }

    function stopCamera() {
      if (qrScanner) {
        qrScanner.stop();
        qrScanner.destroy();
        qrScanner = null;
      }

      // Update UI
      const cameraContainer = document.getElementById('camera-scanner');
      const cameraToggle = document.getElementById('camera-toggle');
      const instructions = document.getElementById('scanner-instructions');

      cameraContainer.classList.remove('active');
      cameraToggle.classList.remove('active');
      cameraToggle.textContent = '📷';
      instructions.textContent = 'Enter Member ID manually or click the camera button to scan QR code';
      cameraActive = false;

      hideCameraStatus();
    }



    function showCameraStatus(message, type) {
      const statusElement = document.getElementById('camera-status');
      statusElement.textContent = message;
      statusElement.className = `camera-status ${type}`;
    }

    function hideCameraStatus() {
      const statusElement = document.getElementById('camera-status');
      statusElement.style.display = 'none';
    }

    function setupScanner() {
      const scannerInput = document.getElementById('scanner-input');

      // Setup manual input
      scannerInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          processCheckin(this.value.trim());
          this.value = '';
        }
      });

      // Auto-focus the input
      scannerInput.focus();
    }

    function extractMemberIdFromUrl(input) {
      // Check if input is a URL containing member ID
      if (input.includes('member.html')) {
        const urlParams = new URLSearchParams(input.split('?')[1]);
        return urlParams.get('id');
      }
      // Check if input looks like a member ID
      if (/^SN\d{4}\d{4}$/.test(input)) {
        return input;
      }
      return null;
    }

    async function processCheckin(input) {
      if (!selectedActivity) {
        showResult('Please select an activity first.', 'error');
        return;
      }

      const memberId = extractMemberIdFromUrl(input);
      if (!memberId) {
        showResult('Invalid QR code or Member ID format.', 'error');
        return;
      }

      // Check if member exists
      if (!members[memberId]) {
        showResult(`Member ${memberId} not found.`, 'error');
        return;
      }

      const member = members[memberId];

      // Check if member is active
      if (!member.isActive) {
        showResult(`Member ${member.name} has expired membership.`, 'error');
        return;
      }

      // Check if already checked in for this activity
      const existingCheckin = Object.values(checkins).find(checkin =>
        checkin.activityId === selectedActivity &&
        checkin.memberId === memberId
      );

      if (existingCheckin) {
        showResult(`${member.name} is already checked in for this activity.`, 'error');
        return;
      }

      // Create new checkin
      const checkinId = generateCheckinId();
      const currentUser = getCurrentUser();
      const newCheckin = {
        id: checkinId,
        activityId: selectedActivity,
        memberId: memberId,
        memberName: member.name,
        memberType: selectedMemberType,
        checkinTime: new Date().toISOString(),
        checkinBy: currentUser.name,
        checkinByEmail: currentUser.email,
        notes: `Checked in via QR code scan as ${selectedMemberType} by ${currentUser.name}`,
        createdAt: new Date().toISOString()
      };

      checkins[checkinId] = newCheckin;

      // Save checkins
      await saveCheckins();

      showResult(`✅ ${member.name} checked in successfully as ${selectedMemberType}!`, 'success');
      renderCheckinHistory();
      updateStats();
    }

    function generateCheckinId() {
      const currentYear = new Date().getFullYear();
      const prefix = `CHK${currentYear}`;

      let maxSequence = 0;
      Object.keys(checkins).forEach(id => {
        if (id.startsWith(prefix)) {
          const sequenceStr = id.substring(prefix.length);
          const sequence = parseInt(sequenceStr, 10);
          if (!isNaN(sequence) && sequence > maxSequence) {
            maxSequence = sequence;
          }
        }
      });

      const newSequence = maxSequence + 1;
      return `${prefix}${newSequence.toString().padStart(4, '0')}`;
    }

    function showResult(message, type) {
      const resultDiv = document.getElementById('checkin-result');
      resultDiv.className = `checkin-result checkin-${type}`;
      resultDiv.textContent = message;
      resultDiv.style.display = 'block';

      // Hide after 5 seconds
      setTimeout(() => {
        resultDiv.style.display = 'none';
      }, 5000);
    }

    function renderCheckinHistory() {
      const checkinList = document.getElementById('checkin-list');
      const historyTitle = document.getElementById('checkin-history-title');
      checkinList.innerHTML = '';

      // Update title based on selected activity
      if (selectedActivity) {
        const activity = activities[selectedActivity];
        const activityName = activity ? activity.name.en : 'Selected Activity';
        historyTitle.textContent = `Check-ins for ${activityName}`;
      } else {
        historyTitle.textContent = 'Recent Check-ins';
      }

      // Filter checkins by selected activity
      let filteredCheckins = Object.values(checkins);

      if (selectedActivity) {
        filteredCheckins = filteredCheckins.filter(checkin =>
          checkin.activityId === selectedActivity
        );
      }

      // Sort by check-in time (most recent first) and limit to 20
      const recentCheckins = filteredCheckins
        .sort((a, b) => new Date(b.checkinTime) - new Date(a.checkinTime))
        .slice(0, 20);

      if (recentCheckins.length === 0) {
        const noDataMessage = document.createElement('div');
        noDataMessage.style.textAlign = 'center';
        noDataMessage.style.color = '#666';
        noDataMessage.style.padding = '2rem';

        if (selectedActivity) {
          noDataMessage.textContent = 'No check-ins for this activity yet.';
        } else {
          noDataMessage.textContent = 'Please select an activity to view check-ins.';
        }

        checkinList.appendChild(noDataMessage);
        return;
      }

      recentCheckins.forEach(checkin => {
        const checkinItem = document.createElement('div');
        checkinItem.className = 'checkin-item';

        // If no activity is selected, show activity name; otherwise, just show member info
        let activityInfo = '';
        if (!selectedActivity) {
          const activity = activities[checkin.activityId];
          const activityName = activity ? activity.name.en : 'Unknown Activity';
          activityInfo = `<p><strong>Activity:</strong> ${activityName}</p>`;
        }

        // Check if feedback exists for this checkin
        const hasFeedback = feedbacks[checkin.id];
        const feedbackIndicator = hasFeedback ? ' <span style="color: #28a745; font-size: 0.8rem;">✓ Feedback</span>' : '';
        const feedbackButtonText = hasFeedback ? '✏️ Edit Feedback' : '💬 Add Feedback';

        // Generate feedback action buttons
        let feedbackActions = `
          <button class="feedback-checkin-btn" onclick="openFeedbackModal('${checkin.id}')">
            ${feedbackButtonText}
          </button>
        `;

        // Add delete feedback button if feedback exists
        if (hasFeedback) {
          feedbackActions += `
            <button class="delete-feedback-btn" onclick="deleteFeedback('${checkin.id}', '${checkin.memberName}')">
              🗑️ Delete Feedback
            </button>
          `;
        }

        checkinItem.innerHTML = `
          <div class="checkin-info">
            <h4>${checkin.memberName} <span class="member-type-badge type-${checkin.memberType}">${checkin.memberType}</span>${feedbackIndicator}</h4>
            ${activityInfo}
            <p><strong>Time:</strong> ${new Date(checkin.checkinTime).toLocaleString()}</p>
          </div>
          <div class="checkin-actions">
            ${feedbackActions}
            <button class="delete-checkin-btn" onclick="deleteCheckin('${checkin.id}', '${checkin.memberName}')">
              🗑️ Delete Check-in
            </button>
          </div>
        `;

        checkinList.appendChild(checkinItem);
      });
    }

    function updateStats() {
      const statsSection = document.getElementById('stats-section');

      let totalCheckins = 0;
      let memberCheckins = 0;
      let coachCheckins = 0;
      let todayCheckins = 0;

      const today = new Date().toDateString();

      Object.values(checkins).forEach(checkin => {
        // Filter by selected activity if any
        if (selectedActivity && checkin.activityId !== selectedActivity) {
          return;
        }

        totalCheckins++;

        if (checkin.memberType === 'member') {
          memberCheckins++;
        } else if (checkin.memberType === 'coach') {
          coachCheckins++;
        }

        if (new Date(checkin.checkinTime).toDateString() === today) {
          todayCheckins++;
        }
      });

      statsSection.innerHTML = `
        <div class="stat-card">
          <div class="stat-number">${totalCheckins}</div>
          <div class="stat-label">Total Check-ins</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">${memberCheckins}</div>
          <div class="stat-label">Members</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">${coachCheckins}</div>
          <div class="stat-label">Coaches</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">${todayCheckins}</div>
          <div class="stat-label">Today</div>
        </div>
      `;
    }

    window.deleteFeedback = async function(checkinId, memberName) {
      // Get feedback details for confirmation
      const feedback = feedbacks[checkinId];
      if (!feedback) {
        showResult('Feedback record not found.', 'error');
        return;
      }

      const checkin = checkins[checkinId];
      const activity = activities[checkin?.activityId];
      const activityName = activity ? activity.name.en : 'Unknown Activity';
      const feedbackDate = new Date(feedback.createdAt).toLocaleString();

      // Confirm deletion with detailed information
      const confirmMessage = `Delete Feedback Record?\n\n` +
        `Student: ${memberName}\n` +
        `Activity: ${activityName}\n` +
        `Feedback Created: ${feedbackDate}\n` +
        `Created by: ${feedback.createdBy || 'Unknown'}\n\n` +
        `This will permanently delete the feedback and skill assessment.\n` +
        `This action cannot be undone. Are you sure?`;

      if (!confirm(confirmMessage)) {
        return;
      }

      try {
        // Delete the feedback
        delete feedbacks[checkinId];

        // Save updated feedbacks
        await saveFeedbacks();

        // Update UI
        renderCheckinHistory();

        showResult(`✅ Feedback for ${memberName} deleted successfully.`, 'success');

      } catch (error) {
        console.error('Error deleting feedback:', error);
        showResult('Failed to delete feedback. Please try again.', 'error');
      }
    };

    window.deleteCheckin = async function(checkinId, memberName) {
      // Get checkin details for confirmation
      const checkin = checkins[checkinId];
      if (!checkin) {
        showResult('Check-in record not found.', 'error');
        return;
      }

      const activity = activities[checkin.activityId];
      const activityName = activity ? activity.name.en : 'Unknown Activity';
      const checkinTime = new Date(checkin.checkinTime).toLocaleString();

      // Confirm deletion with detailed information
      const confirmMessage = `Delete Check-in Record?\n\n` +
        `Member: ${memberName}\n` +
        `Type: ${checkin.memberType}\n` +
        `Activity: ${activityName}\n` +
        `Time: ${checkinTime}\n\n` +
        `This action cannot be undone. Are you sure?`;

      if (!confirm(confirmMessage)) {
        return;
      }

      try {
        // Check if there's associated feedback
        const hasFeedback = feedbacks[checkinId];

        // Delete the checkin
        delete checkins[checkinId];

        // Also delete associated feedback if it exists
        if (hasFeedback) {
          delete feedbacks[checkinId];
          await saveFeedbacks();
        }

        // Save updated checkins
        await saveCheckins();

        // Update UI
        renderCheckinHistory();
        updateStats();

        const feedbackNote = hasFeedback ? ' (including associated feedback)' : '';
        showResult(`✅ Check-in record for ${memberName} deleted successfully${feedbackNote}.`, 'success');

      } catch (error) {
        console.error('Error deleting checkin:', error);
        showResult('Failed to delete check-in record. Please try again.', 'error');
      }
    };

    // Feedback functions
    window.openFeedbackModal = function(checkinId) {
      const checkin = checkins[checkinId];
      if (!checkin) {
        showResult('Check-in record not found.', 'error');
        return;
      }

      currentFeedbackCheckinId = checkinId;

      // Get activity and member info
      const activity = activities[checkin.activityId];
      const member = members[checkin.memberId];

      // Populate member info
      const memberInfoDiv = document.getElementById('feedback-member-info');
      let feedbackHistory = '';

      // Load existing feedback if any
      const existingFeedback = feedbacks[checkinId];
      if (existingFeedback) {
        feedbackHistory = `
          <p><strong>Created by:</strong> ${existingFeedback.createdBy || 'Unknown'} on ${new Date(existingFeedback.createdAt).toLocaleString()}</p>
        `;
        if (existingFeedback.updatedBy && existingFeedback.updatedAt !== existingFeedback.createdAt) {
          feedbackHistory += `<p><strong>Last updated by:</strong> ${existingFeedback.updatedBy} on ${new Date(existingFeedback.updatedAt).toLocaleString()}</p>`;
        }
      }

      memberInfoDiv.innerHTML = `
        <h3>${checkin.memberName}</h3>
        <p><strong>Member ID:</strong> ${checkin.memberId}</p>
        <p><strong>Activity:</strong> ${activity ? activity.name.en : 'Unknown Activity'}</p>
        <p><strong>Check-in Time:</strong> ${new Date(checkin.checkinTime).toLocaleString()}</p>
        <p><strong>Type:</strong> ${checkin.memberType}</p>
        ${feedbackHistory}
      `;

      // Set feedback text in textarea
      const feedbackTextarea = document.getElementById('feedback-text');
      feedbackTextarea.value = existingFeedback ? (existingFeedback.overallFeedback || existingFeedback.feedback || '') : '';

      // Initialize skill assessment
      initializeSkillAssessment();

      // Load existing skill assessment data
      if (existingFeedback) {
        loadSkillAssessmentData(existingFeedback);
      }

      // Show modal
      document.getElementById('feedback-modal').style.display = 'block';
      feedbackTextarea.focus();
    };

    window.closeFeedbackModal = function() {
      document.getElementById('feedback-modal').style.display = 'none';
      currentFeedbackCheckinId = null;

      // Clear all form data
      document.getElementById('feedback-text').value = '';

      // Clear all checkboxes
      document.querySelectorAll('.skill-item input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = false;
        updateSkillItemStatus(checkbox);
      });

      // Clear all section textareas
      document.querySelectorAll('.section-textarea').forEach(textarea => {
        textarea.value = '';
      });

      // Reset all section progress
      document.querySelectorAll('.skill-section').forEach(section => {
        updateSectionProgress(section);
      });
    };

    // Close modal when clicking outside
    window.onclick = function(event) {
      const modal = document.getElementById('feedback-modal');
      if (event.target === modal) {
        closeFeedbackModal();
      }
    };

    window.saveFeedback = async function(event) {
      event.preventDefault();

      if (!currentFeedbackCheckinId) {
        showResult('No check-in selected for feedback.', 'error');
        return;
      }

      const feedbackText = document.getElementById('feedback-text').value.trim();
      const skillAssessment = collectSkillAssessmentData();

      // Check if there's any feedback content (either overall feedback or skill assessment)
      if (!feedbackText && skillAssessment.completedSkills.length === 0 && Object.keys(skillAssessment.sectionFeedbacks).length === 0) {
        showResult('Please enter feedback or complete skill assessment before saving.', 'error');
        return;
      }

      const checkin = checkins[currentFeedbackCheckinId];
      if (!checkin) {
        showResult('Check-in record not found.', 'error');
        return;
      }

      try {
        // Get current user information
        const currentUser = getCurrentUser();

        // Check if feedback already exists
        const existingFeedback = feedbacks[currentFeedbackCheckinId];

        if (existingFeedback) {
          // Update existing feedback
          existingFeedback.overallFeedback = feedbackText;
          existingFeedback.skillAssessment = skillAssessment;
          existingFeedback.updatedAt = new Date().toISOString();
          existingFeedback.updatedBy = currentUser.name;
          existingFeedback.updatedByEmail = currentUser.email;

          // Keep backward compatibility
          existingFeedback.feedback = feedbackText;
        } else {
          // Create new feedback record
          const feedbackRecord = {
            id: generateFeedbackId(),
            checkinId: currentFeedbackCheckinId,
            activityId: checkin.activityId,
            memberId: checkin.memberId,
            memberName: checkin.memberName,
            overallFeedback: feedbackText,
            skillAssessment: skillAssessment,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: currentUser.name,
            createdByEmail: currentUser.email,

            // Keep backward compatibility
            feedback: feedbackText
          };

          // Save new feedback
          feedbacks[currentFeedbackCheckinId] = feedbackRecord;
        }

        // Save to server
        await saveFeedbacks();

        showResult(`✅ Feedback saved for ${checkin.memberName}!`, 'success');
        closeFeedbackModal();

        // Update the UI to show feedback has been added (optional visual indicator)
        renderCheckinHistory();

      } catch (error) {
        console.error('Error saving feedback:', error);
        showResult('Failed to save feedback. Please try again.', 'error');
      }
    };

    function generateFeedbackId() {
      const currentYear = new Date().getFullYear();
      const prefix = `FB${currentYear}`;

      let maxSequence = 0;
      Object.values(feedbacks).forEach(feedback => {
        if (feedback.id && feedback.id.startsWith(prefix)) {
          const sequenceStr = feedback.id.substring(prefix.length);
          const sequence = parseInt(sequenceStr, 10);
          if (!isNaN(sequence) && sequence > maxSequence) {
            maxSequence = sequence;
          }
        }
      });

      const newSequence = maxSequence + 1;
      return `${prefix}${newSequence.toString().padStart(4, '0')}`;
    }

    async function saveFeedbacks() {
      try {
        // Use the server.py API endpoint
        const response = await fetch('data/feedbacks.json', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(feedbacks, null, 2)
        });

        if (response.ok) {
          return; // Success!
        }

        // If server save fails, use localStorage as fallback
        throw new Error('Server save failed');

      } catch (error) {
        console.error('Error saving feedbacks:', error);

        // Fallback to localStorage
        try {
          localStorage.setItem('snownavi_feedbacks', JSON.stringify(feedbacks));
          showResult('⚠️ Feedback saved locally due to server error. Changes may not persist across sessions.', 'warning');
        } catch (localError) {
          console.error('Failed to save to localStorage:', localError);
          throw new Error('Failed to save feedback both to server and locally');
        }
      }
    }

    async function saveCheckins() {
      try {
        // Use the server.py API endpoint
        const response = await fetch('data/checkins.json', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(checkins, null, 2)
        });

        if (!response.ok) {
          throw new Error('Failed to save checkins');
        }
      } catch (error) {
        console.error('Error saving checkins:', error);
        throw error;
      }
    }

    // Initialize the page
    window.onload = checkAuth;

    // Clean up camera when page is unloaded
    window.addEventListener('beforeunload', () => {
      stopCamera();
    });

    // Also clean up when page becomes hidden (mobile browsers)
    document.addEventListener('visibilitychange', () => {
      if (document.hidden && qrScanner) {
        qrScanner.stop();
      } else if (!document.hidden && qrScanner && cameraActive) {
        qrScanner.start();
      }
    });
  </script>
</body>
</html>
