<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate Design Showcase - SnowNavi</title>
    <style>
        :root {
            --main-red: #E53512;
            --bg-light: #F9F4F3;
            --text-dark: #2F2F2F;
            --text-gray: #717171;
            --contrast-white: #FFFFFF;
            --accent-blue: #9ED4E7;
        }

        body { 
            font-family: 'Noto Sans SC', sans-serif; 
            margin: 2rem; 
            background: var(--bg-light);
            color: var(--text-dark);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--contrast-white);
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 3px solid var(--main-red);
        }
        h1 { color: var(--main-red); }
        h2 { color: var(--text-dark); border-bottom: 2px solid var(--main-red); padding-bottom: 0.5rem; }
        .showcase-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin: 2rem 0;
        }
        .design-section {
            background: var(--bg-light);
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid rgba(229, 53, 18, 0.1);
        }
        .design-section h3 {
            color: var(--main-red);
            margin-bottom: 1rem;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(229, 53, 18, 0.1);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-icon {
            font-size: 1.2rem;
            width: 24px;
        }
        .visual-demo {
            background: var(--contrast-white);
            border: 3px solid var(--main-red);
            border-radius: 12px;
            padding: 2rem;
            margin: 2rem 0;
            text-align: center;
            position: relative;
            min-height: 400px;
        }
        .visual-demo::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            border: 2px solid var(--accent-blue);
            border-radius: 8px;
            pointer-events: none;
        }
        .demo-logo {
            width: 60px;
            height: 60px;
            background: var(--main-red);
            border-radius: 50%;
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.5rem;
        }
        .demo-title {
            color: var(--main-red);
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .demo-subtitle {
            color: var(--text-dark);
            font-size: 1.2rem;
            margin-bottom: 1rem;
        }
        .demo-brand {
            color: var(--text-dark);
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 2rem;
        }
        .demo-student {
            color: var(--main-red);
            font-size: 1.8rem;
            font-weight: bold;
            margin: 1rem 0;
        }
        .demo-qr-section {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin: 2rem 0;
        }
        .demo-qr {
            text-align: center;
        }
        .demo-qr-box {
            width: 60px;
            height: 60px;
            background: var(--text-dark);
            margin: 0 auto 0.5rem;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
        }
        .qr-label {
            font-size: 0.8rem;
            color: var(--text-gray);
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin: 2rem 0;
        }
        .comparison-item {
            background: var(--contrast-white);
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid var(--main-red);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }
        .comparison-item h4 {
            color: var(--main-red);
            margin: 0 0 1rem 0;
        }
        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 1rem 0;
        }
        .before, .after {
            padding: 1rem;
            border-radius: 6px;
            text-align: center;
        }
        .before {
            background: #ffebee;
            border: 1px solid #ffcdd2;
        }
        .after {
            background: #e8f5e8;
            border: 1px solid #c8e6c9;
        }
        .portal-link {
            background: var(--contrast-white);
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
            margin: 2rem 0;
            border: 2px solid var(--main-red);
            box-shadow: 0 2px 8px rgba(229, 53, 18, 0.1);
        }
        .portal-link a {
            color: var(--contrast-white);
            text-decoration: none;
            font-weight: bold;
            font-size: 1.1rem;
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: var(--main-red);
            border-radius: 6px;
            transition: background-color 0.2s;
            margin: 0.5rem;
        }
        .portal-link a:hover {
            background: #c42e0f;
        }
        @media (max-width: 768px) {
            .showcase-grid, .comparison-grid, .before-after {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 Certificate Design Showcase</h1>
            <p>Professional SnowNavi certificate with authentic branding and QR codes</p>
        </div>
        
        <div class="visual-demo">
            <div class="demo-logo">SN</div>
            <div class="demo-title">CERTIFICATE</div>
            <div class="demo-subtitle">OF SNOWBOARDING ACHIEVEMENT</div>
            <div class="demo-brand">SnowNavi Snow Club</div>
            <div style="color: var(--text-gray); margin: 1rem 0;">This certifies that</div>
            <div class="demo-student">Zhang Wei</div>
            <div style="color: var(--text-gray); font-size: 0.9rem;">Member ID: SN20250001</div>
            
            <div class="demo-qr-section">
                <div class="demo-qr">
                    <div class="demo-qr-box">QR</div>
                    <div class="qr-label">Member Verification</div>
                </div>
                <div class="demo-qr">
                    <div class="demo-qr-box">微信</div>
                    <div class="qr-label">WeChat Official</div>
                </div>
                <div class="demo-qr">
                    <div class="demo-qr-box">小红书</div>
                    <div class="qr-label">Xiaohongshu</div>
                </div>
            </div>
        </div>
        
        <div class="showcase-grid">
            <div class="design-section">
                <h3>🎯 Authentic Branding Elements</h3>
                <ul class="feature-list">
                    <li><span class="feature-icon">🏷️</span> Real SnowNavi logo from assets/picture/snownavi_logo.png</li>
                    <li><span class="feature-icon">🎨</span> Official brand colors (#E53512, #F9F4F3, #9ED4E7)</li>
                    <li><span class="feature-icon">📐</span> Professional 3:4 aspect ratio (1200x1600px)</li>
                    <li><span class="feature-icon">✨</span> Decorative borders and gradient backgrounds</li>
                </ul>
            </div>
            
            <div class="design-section">
                <h3>📱 Real QR Code Integration</h3>
                <ul class="feature-list">
                    <li><span class="feature-icon">👤</span> Unique member verification QR pattern</li>
                    <li><span class="feature-icon">💬</span> Authentic WeChat QR from wechat_qrcode.jpg</li>
                    <li><span class="feature-icon">📖</span> Real Xiaohongshu QR from xiaohongshu_qrcode.jpg</li>
                    <li><span class="feature-icon">🔗</span> Direct links to SnowNavi social media</li>
                </ul>
            </div>
        </div>
        
        <div class="comparison-grid">
            <div class="comparison-item">
                <h4>Before: Generic Design</h4>
                <div class="before-after">
                    <div class="before">
                        <strong>Before</strong><br>
                        • Placeholder patterns<br>
                        • Generic QR codes<br>
                        • Basic styling<br>
                        • No real branding
                    </div>
                    <div class="after">
                        <strong>After</strong><br>
                        • Authentic SnowNavi logo<br>
                        • Real social media QR codes<br>
                        • Professional design<br>
                        • Complete brand integration
                    </div>
                </div>
            </div>
            
            <div class="comparison-item">
                <h4>Enhanced User Experience</h4>
                <div class="before-after">
                    <div class="before">
                        <strong>Previous</strong><br>
                        • Static placeholder images<br>
                        • No social media connection<br>
                        • Limited verification<br>
                        • Basic certificate
                    </div>
                    <div class="after">
                        <strong>Current</strong><br>
                        • Dynamic image loading<br>
                        • Direct social media access<br>
                        • Member verification QR<br>
                        • Professional certificate
                    </div>
                </div>
            </div>
        </div>
        
        <div class="design-section">
            <h2>🔧 Technical Implementation</h2>
            
            <div class="showcase-grid">
                <div>
                    <h4>Image Loading System</h4>
                    <ul class="feature-list">
                        <li><span class="feature-icon">📁</span> Preload all required images</li>
                        <li><span class="feature-icon">💾</span> Image caching for performance</li>
                        <li><span class="feature-icon">🔄</span> Fallback patterns if images fail</li>
                        <li><span class="feature-icon">⚡</span> Optimized loading sequence</li>
                    </ul>
                </div>
                
                <div>
                    <h4>QR Code Generation</h4>
                    <ul class="feature-list">
                        <li><span class="feature-icon">🎯</span> Member-specific QR patterns</li>
                        <li><span class="feature-icon">📐</span> Standard QR code structure</li>
                        <li><span class="feature-icon">🔍</span> Finder patterns for scanning</li>
                        <li><span class="feature-icon">🆔</span> Unique member ID encoding</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="design-section">
            <h2>📊 Certificate Layout</h2>
            
            <div style="background: var(--contrast-white); padding: 1rem; border-radius: 6px; margin: 1rem 0;">
                <h4>Vertical Layout Structure:</h4>
                <ol style="line-height: 1.8;">
                    <li><strong>Header (0-400px):</strong> Certificate title, SnowNavi logo, branding</li>
                    <li><strong>Student Info (400-750px):</strong> Name, member ID, course details</li>
                    <li><strong>Skills Progress (750-1000px):</strong> Visual progress bars and statistics</li>
                    <li><strong>Verification (1000-1200px):</strong> Member QR code and verification text</li>
                    <li><strong>Contact (1200-1450px):</strong> Email, website, social media QR codes</li>
                    <li><strong>Footer (1450-1600px):</strong> Issue date and program description</li>
                </ol>
            </div>
        </div>
        
        <div class="portal-link">
            <h3 style="color: var(--main-red); margin-bottom: 1rem;">Experience the Enhanced Certificate</h3>
            <a href="test_certificate.html" target="_blank">🧪 Test Certificate Generator</a>
            <a href="student_feedback.html" target="_blank">🎿 Student Portal</a>
            <a href="demo_certificate.html" target="_blank">📚 Feature Demo</a>
        </div>
        
        <div class="design-section">
            <h2>✨ Key Improvements</h2>
            
            <div class="comparison-grid">
                <div class="comparison-item">
                    <h4>Brand Authenticity</h4>
                    <p>Real SnowNavi logo and official brand colors create a professional, trustworthy certificate that students are proud to share.</p>
                </div>
                
                <div class="comparison-item">
                    <h4>Social Media Integration</h4>
                    <p>Authentic QR codes connect directly to SnowNavi's WeChat and Xiaohongshu accounts, driving real engagement.</p>
                </div>
                
                <div class="comparison-item">
                    <h4>Member Verification</h4>
                    <p>Unique QR patterns for each member enable verification and add security to the certificate system.</p>
                </div>
                
                <div class="comparison-item">
                    <h4>Professional Quality</h4>
                    <p>High-resolution output (1200x1600px) suitable for both digital sharing and professional printing.</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
