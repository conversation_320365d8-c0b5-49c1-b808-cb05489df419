<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logo Layout Demo - <PERSON><PERSON>avi</title>
    <style>
        :root {
            --main-red: #E53512;
            --bg-light: #F9F4F3;
            --text-dark: #2F2F2F;
            --text-gray: #717171;
            --contrast-white: #FFFFFF;
            --accent-blue: #9ED4E7;
        }

        body { 
            font-family: 'Noto Sans SC', sans-serif; 
            margin: 2rem; 
            background: var(--bg-light);
            color: var(--text-dark);
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--contrast-white);
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 3px solid var(--main-red);
        }
        h1 { color: var(--main-red); }
        .demo-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid var(--main-red);
            background: var(--bg-light);
        }
        .demo-section h3 {
            color: var(--main-red);
            margin: 0 0 1rem 0;
        }
        .layout-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin: 2rem 0;
        }
        .layout-demo {
            background: var(--contrast-white);
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid rgba(229, 53, 18, 0.2);
            text-align: center;
        }
        .layout-demo h4 {
            color: var(--main-red);
            margin: 0 0 1rem 0;
        }
        .logo-name-demo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            margin: 1rem 0;
            padding: 1rem;
            background: var(--bg-light);
            border-radius: 6px;
        }
        .demo-logo {
            width: 60px;
            height: 60px;
            background: var(--main-red);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.5rem;
        }
        .demo-logo.large {
            width: 80px;
            height: 80px;
            font-size: 2rem;
        }
        .demo-name {
            font-size: 1.8rem;
            font-weight: bold;
            color: var(--main-red);
        }
        .demo-name.large {
            font-size: 2.2rem;
        }
        .old-layout {
            text-align: center;
        }
        .old-layout .demo-logo {
            margin: 0 auto 1rem;
        }
        .improvement-list {
            list-style: none;
            padding: 0;
        }
        .improvement-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .improvement-icon {
            font-size: 1.2rem;
            width: 24px;
        }
        .btn {
            background: var(--main-red);
            color: var(--contrast-white);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 0.5rem;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #c42e0f;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(229, 53, 18, 0.3);
        }
        @media (max-width: 768px) {
            .layout-comparison {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 Logo Layout Improvement</h1>
            <p>SnowNavi logo and name now arranged in one line</p>
        </div>
        
        <div class="demo-section">
            <h3>📐 Layout Comparison</h3>
            
            <div class="layout-comparison">
                <div class="layout-demo">
                    <h4>❌ Previous Layout</h4>
                    <div class="old-layout">
                        <div class="demo-logo">SN</div>
                        <div class="demo-name">SnowNavi Snow Club</div>
                    </div>
                    <p style="color: var(--text-gray); font-size: 0.9rem;">
                        Logo and name stacked vertically<br>
                        Takes more vertical space<br>
                        80px logo size
                    </p>
                </div>
                
                <div class="layout-demo">
                    <h4>✅ New Layout</h4>
                    <div class="logo-name-demo">
                        <div class="demo-logo large">SN</div>
                        <div class="demo-name large">SnowNavi Snow Club</div>
                    </div>
                    <p style="color: var(--text-gray); font-size: 0.9rem;">
                        Logo and name in one line<br>
                        More compact and balanced<br>
                        100px logo size (larger)
                    </p>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>✨ Improvements</h3>
            <ul class="improvement-list">
                <li><span class="improvement-icon">📏</span>More efficient use of vertical space</li>
                <li><span class="improvement-icon">🎯</span>Better visual balance between logo and text</li>
                <li><span class="improvement-icon">📈</span>Increased logo size from 80px to 100px</li>
                <li><span class="improvement-icon">🎨</span>Enhanced brand visibility and recognition</li>
                <li><span class="improvement-icon">📱</span>More professional appearance</li>
                <li><span class="improvement-icon">⚖️</span>Horizontally centered as a combined element</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <h3>🔧 Technical Implementation</h3>
            <div style="background: var(--contrast-white); padding: 1rem; border-radius: 6px; font-family: monospace; font-size: 0.9rem;">
<strong>Logo Size:</strong> 100px × 100px (increased from 80px)<br>
<strong>Font Size:</strong> 36px bold (increased from 32px)<br>
<strong>Gap:</strong> 20px between logo and text<br>
<strong>Alignment:</strong> Combined element centered horizontally<br>
<strong>Text Position:</strong> Vertically centered with logo<br><br>

<strong>Calculation:</strong><br>
totalWidth = logoSize + gap + textWidth<br>
logoX = (canvasWidth - totalWidth) / 2<br>
textX = logoX + logoSize + gap<br>
textY = logoY + logoSize/2 + fontSize/3
            </div>
        </div>
        
        <div class="demo-section">
            <h3>📋 Certificate Layout Impact</h3>
            <div style="background: var(--contrast-white); padding: 1rem; border-radius: 6px;">
                <h4 style="color: var(--main-red); margin: 0 0 1rem 0;">Before vs After in Certificate:</h4>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin: 1rem 0;">
                    <div style="background: #ffebee; padding: 1rem; border-radius: 4px;">
                        <strong>Before:</strong><br>
                        [Logo]<br>
                        SnowNavi Snow Club<br>
                        Professional Instruction...<br>
                        <em style="color: var(--text-gray);">More vertical space used</em>
                    </div>
                    <div style="background: #e8f5e8; padding: 1rem; border-radius: 4px;">
                        <strong>After:</strong><br>
                        [Logo] SnowNavi Snow Club<br>
                        Professional Instruction...<br>
                        <em style="color: var(--text-gray);">Compact and balanced</em>
                    </div>
                </div>
                
                <p><strong>Result:</strong> More space available for other certificate content while maintaining strong brand presence.</p>
            </div>
        </div>
        
        <div style="text-align: center; margin: 2rem 0;">
            <h3 style="color: var(--main-red); margin-bottom: 1rem;">🧪 Test the New Layout</h3>
            <a href="certificate_preview.html" target="_blank" class="btn">🎨 Preview Certificate</a>
            <a href="student_feedback.html" target="_blank" class="btn">🎿 Student Portal</a>
        </div>
        
        <div style="background: var(--bg-light); padding: 1.5rem; border-radius: 8px; text-align: center;">
            <h4 style="color: var(--main-red); margin: 0 0 1rem 0;">🎯 Layout Optimization Complete</h4>
            <p>The SnowNavi logo and name are now arranged in a single line with improved visual balance. The larger logo (100px) provides better brand visibility while the horizontal layout makes more efficient use of space in the certificate design.</p>
        </div>
    </div>
</body>
</html>
