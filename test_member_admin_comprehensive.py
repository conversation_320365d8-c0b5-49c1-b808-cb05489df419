#!/usr/bin/env python3
"""
Comprehensive test for member admin fixes
Tests both issues:
1. New member not showing in list after creation
2. Custom fields (like email) not showing after page reload
"""

import requests
import json
import time

BASE_URL = "http://localhost:8899"

def test_member_data_loading():
    """Test that member data loads correctly with custom fields"""
    print("🧪 Testing member data loading...")
    
    try:
        response = requests.get(f"{BASE_URL}/data/members.json")
        if response.status_code == 200:
            members = response.json()
            print(f"✅ Successfully loaded {len(members)} members")
            
            # Check for custom fields
            custom_fields = set()
            standard_fields = {'id', 'name', 'isActive', 'validityPeriod', 'password'}
            
            for member_id, member_data in members.items():
                for field in member_data.keys():
                    if field not in standard_fields:
                        custom_fields.add(field)
            
            print(f"✅ Found custom fields: {', '.join(custom_fields)}")
            
            # Check specific members with email
            members_with_email = [m for m in members.values() if 'email' in m]
            print(f"✅ Members with email field: {len(members_with_email)}")
            
            if members_with_email:
                sample = members_with_email[0]
                print(f"   Sample: {sample['name']} - {sample['email']}")
            
            return True
        else:
            print(f"❌ Failed to load members: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error loading members: {e}")
        return False

def test_save_members():
    """Test that saving members works correctly"""
    print("\n🧪 Testing member saving...")
    
    try:
        # First load current members
        response = requests.get(f"{BASE_URL}/data/members.json")
        if response.status_code != 200:
            print("❌ Failed to load current members")
            return False
        
        members = response.json()
        original_count = len(members)
        
        # Test saving (without actually modifying data)
        save_response = requests.post(
            f"{BASE_URL}/data/members.json",
            headers={'Content-Type': 'application/json'},
            json=members
        )
        
        if save_response.status_code == 200:
            print("✅ Save operation successful")
            
            # Verify data integrity after save
            verify_response = requests.get(f"{BASE_URL}/data/members.json")
            if verify_response.status_code == 200:
                saved_members = verify_response.json()
                if len(saved_members) == original_count:
                    print("✅ Data integrity maintained after save")
                    return True
                else:
                    print(f"❌ Data count mismatch: {original_count} -> {len(saved_members)}")
                    return False
            else:
                print("❌ Failed to verify saved data")
                return False
        else:
            print(f"❌ Save failed: {save_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing save: {e}")
        return False

def test_custom_fields_detection():
    """Test that custom fields are properly detected"""
    print("\n🧪 Testing custom fields detection...")
    
    try:
        response = requests.get(f"{BASE_URL}/data/members.json")
        if response.status_code != 200:
            print("❌ Failed to load members")
            return False
        
        members = response.json()
        
        # Simulate the updateCustomFieldsList function
        standard_fields = {'id', 'name', 'isActive', 'validityPeriod', 'password'}
        found_fields = {'gender'}  # Default field
        
        for member in members.values():
            for field in member.keys():
                if field not in standard_fields:
                    found_fields.add(field)
        
        custom_fields = list(found_fields)
        
        print(f"✅ Custom fields detected: {', '.join(custom_fields)}")
        
        # Check if email is detected
        if 'email' in custom_fields:
            print("✅ Email field properly detected")
            return True
        else:
            print("❌ Email field not detected")
            return False
    except Exception as e:
        print(f"❌ Error testing custom fields: {e}")
        return False

def test_password_generation():
    """Test password generation API"""
    print("\n🧪 Testing password generation...")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/members/generate-passwords",
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Password generation successful for {len(result['members'])} members")
            
            # Check if all members have passwords
            for member in result['members']:
                if 'password' in member and member['password']:
                    continue
                else:
                    print(f"❌ Member {member['id']} missing password")
                    return False
            
            print("✅ All members have passwords")
            return True
        else:
            print(f"❌ Password generation failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing password generation: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting comprehensive member admin tests...\n")
    
    tests = [
        test_member_data_loading,
        test_custom_fields_detection,
        test_save_members,
        test_password_generation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        time.sleep(0.5)  # Small delay between tests
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The fixes appear to be working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
