<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unified Navigation Demo - SnowNavi</title>
    <style>
        :root {
            --main-red: #E53512;
            --bg-light: #F9F4F3;
            --text-dark: #2F2F2F;
            --text-gray: #717171;
            --contrast-white: #FFFFFF;
            --accent-blue: #9ED4E7;
            --success-green: #28a745;
            --warning-orange: #ffc107;
        }

        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            background: var(--bg-light);
            color: var(--text-dark);
        }

        .header {
            background: var(--contrast-white);
            padding: 1rem 2rem;
            border-bottom: 3px solid var(--main-red);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo-section img {
            width: 50px;
            height: 50px;
        }

        .logo-section h1 {
            color: var(--main-red);
            font-size: 1.8rem;
            margin: 0;
        }

        .nav-links {
            display: flex;
            gap: 1rem;
        }

        .nav-link {
            color: var(--text-gray);
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .nav-link:hover {
            background: var(--bg-light);
            color: var(--main-red);
        }

        .nav-link.active {
            color: var(--main-red);
            font-weight: bold;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .user-info img {
            width: 32px;
            height: 32px;
            border-radius: 50%;
        }

        .logout-btn {
            background: none;
            border: none;
            color: var(--main-red);
            cursor: pointer;
            text-decoration: underline;
            padding: 0.25rem 0.5rem;
        }

        .logout-btn:hover {
            background: var(--bg-light);
            border-radius: 4px;
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        .demo-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid var(--main-red);
            background: var(--contrast-white);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .demo-section h3 {
            color: var(--main-red);
            margin: 0 0 1rem 0;
        }

        .page-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .page-card {
            background: var(--contrast-white);
            border-radius: 8px;
            padding: 1.5rem;
            border: 2px solid var(--accent-blue);
            transition: transform 0.2s ease;
            text-align: center;
        }

        .page-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .page-card h4 {
            color: var(--main-red);
            margin-bottom: 1rem;
        }

        .btn {
            background: var(--main-red);
            color: var(--contrast-white);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 0.5rem;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            background: #c42e0f;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(229, 53, 18, 0.3);
        }

        .btn-secondary {
            background: var(--text-gray);
        }

        .btn-secondary:hover {
            background: #5a5a5a;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }

        .feature-list li:before {
            content: "✅ ";
            color: var(--success-green);
            font-weight: bold;
        }

        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 1rem 0;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .container {
                padding: 0 1rem;
            }

            .page-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo-section">
                <img src="assets/picture/snownavi_logo.png" alt="SnowNavi Logo">
                <h1>SnowNavi Admin</h1>
            </div>
            <nav class="nav-links">
                <a href="admin.html" class="nav-link">🏠 Dashboard</a>
                <a href="course_admin.html" class="nav-link">📚 Courses</a>
                <a href="member_admin.html" class="nav-link">👥 Members</a>
                <a href="activity_admin.html" class="nav-link">📅 Activities</a>
                <a href="checkin_admin.html" class="nav-link">✅ Check-ins</a>
                <a href="feedback_template_admin.html" class="nav-link">📝 Templates</a>
            </nav>
            <div class="user-info">
                <img src="https://via.placeholder.com/32" alt="Profile">
                <span>Demo User</span>
                <button class="logout-btn">Logout</button>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="demo-section">
            <h3>🧭 Unified Navigation System</h3>
            <p>All SnowNavi admin pages now feature a consistent navigation bar that provides easy access to all management sections. The navigation system includes:</p>
            
            <ul class="feature-list">
                <li>Consistent branding with SnowNavi logo and title</li>
                <li>Easy navigation between all admin sections</li>
                <li>Active page highlighting</li>
                <li>User information display with logout functionality</li>
                <li>Responsive design for mobile devices</li>
                <li>Unified color scheme and styling</li>
            </ul>
        </div>

        <div class="demo-section">
            <h3>📱 Responsive Design</h3>
            <p>The navigation adapts to different screen sizes:</p>
            <div class="code-block">
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
  }
  
  .nav-links {
    flex-wrap: wrap;
    justify-content: center;
  }
}
            </div>
        </div>

        <div class="demo-section">
            <h3>🎨 Design Features</h3>
            <ul class="feature-list">
                <li>CSS variables for consistent theming</li>
                <li>Hover effects and smooth transitions</li>
                <li>Professional color scheme</li>
                <li>Clear visual hierarchy</li>
                <li>Accessible design patterns</li>
            </ul>
        </div>

        <div class="page-grid">
            <div class="page-card">
                <h4>🏠 Dashboard</h4>
                <p>Central hub for accessing all admin functions with overview cards for each management section.</p>
                <a href="admin.html" class="btn">Visit Dashboard</a>
            </div>

            <div class="page-card">
                <h4>📚 Course Management</h4>
                <p>Manage course information, subcourses, and related content with multi-language support.</p>
                <a href="course_admin.html" class="btn">Manage Courses</a>
            </div>

            <div class="page-card">
                <h4>👥 Member Management</h4>
                <p>Handle member information, membership status, validity periods, and password generation.</p>
                <a href="member_admin.html" class="btn">Manage Members</a>
            </div>

            <div class="page-card">
                <h4>📅 Activity Management</h4>
                <p>Create and manage activities, events, course schedules, and feedback template assignments.</p>
                <a href="activity_admin.html" class="btn">Manage Activities</a>
            </div>

            <div class="page-card">
                <h4>✅ Check-in Management</h4>
                <p>QR code scanning for member and coach check-ins with integrated feedback system.</p>
                <a href="checkin_admin.html" class="btn">Manage Check-ins</a>
            </div>

            <div class="page-card">
                <h4>📝 Template Management</h4>
                <p>Create and manage feedback templates for different course types and skill levels.</p>
                <a href="feedback_template_admin.html" class="btn">Manage Templates</a>
            </div>
        </div>

        <div style="text-align: center; margin: 2rem 0;">
            <h3 style="color: var(--main-red); margin-bottom: 1rem;">🚀 Navigation System Complete</h3>
            <p>All admin pages now feature the unified navigation system for seamless management experience.</p>
        </div>
    </div>
</body>
</html>
