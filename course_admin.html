<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Course Admin Panel</title>

  <!-- Favicon -->
  <link rel="icon" type="image/png" sizes="32x32" href="assets/picture/snownavi_logo.png">
  <link rel="icon" type="image/png" sizes="16x16" href="assets/picture/snownavi_logo.png">
  <link rel="shortcut icon" href="assets/picture/snownavi_logo.png">

  <style>
    :root {
      --main-red: #E53512;
      --bg-light: #F9F4F3;
      --text-dark: #2F2F2F;
      --text-gray: #717171;
      --contrast-white: #FFFFFF;
      --accent-blue: #9ED4E7;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background: var(--bg-light);
      color: var(--text-dark);
      height: 100vh;
      display: flex;
      flex-direction: column;
    }

    .header {
      background: var(--contrast-white);
      padding: 1rem 2rem;
      border-bottom: 3px solid var(--main-red);
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .logo-section {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .logo-section img {
      width: 50px;
      height: 50px;
    }

    .logo-section h1 {
      color: var(--main-red);
      font-size: 1.8rem;
      margin: 0;
    }

    .nav-links {
      display: flex;
      gap: 1rem;
    }

    .nav-link {
      color: var(--text-gray);
      text-decoration: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      transition: all 0.2s ease;
    }

    .nav-link:hover {
      background: var(--bg-light);
      color: var(--main-red);
    }

    /* Mobile responsive styles */
    @media (max-width: 768px) {
      .header {
        padding: 0.75rem 1rem;
      }

      .header-content {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
      }

      .logo-section {
        justify-content: center;
      }

      .logo-section h1 {
        font-size: 1.5rem;
      }

      .nav-links {
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.5rem;
        order: 2;
      }

      .nav-link {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
        white-space: nowrap;
      }

      .user-info {
        justify-content: center;
        order: 1;
        font-size: 0.9rem;
      }

      .user-info img {
        width: 28px;
        height: 28px;
      }
    }

    @media (max-width: 480px) {
      .header {
        padding: 0.5rem;
      }

      .logo-section img {
        width: 40px;
        height: 40px;
      }

      .logo-section h1 {
        font-size: 1.3rem;
      }

      .nav-links {
        gap: 0.25rem;
      }

      .nav-link {
        padding: 0.3rem 0.6rem;
        font-size: 0.8rem;
      }

      .user-info {
        font-size: 0.8rem;
      }

      .user-info img {
        width: 24px;
        height: 24px;
      }
    }
    h1 {
      color: #E53512;
      padding: 1rem;
      margin: 0;
    }
    .lang-tabs {
      padding: 0 1rem;
      margin-bottom: 1rem;
    }
    .lang-tabs button {
      margin-right: 1rem;
      padding: 0.5rem 1rem;
      background: #eee;
      border: 1px solid #ccc;
      cursor: pointer;
    }
    .lang-tabs button.active {
      background: #E53512;
      color: white;
    }
    .main-container {
      display: flex;
      flex: 1;
      overflow: hidden;
    }
    .tree-view {
      width: 250px;
      background: #fff;
      border-right: 1px solid #ddd;
      overflow-y: auto;
      padding: 1rem;
    }
    .tree-item {
      padding: 0.5rem;
      cursor: pointer;
      border-radius: 4px;
      margin-bottom: 0.25rem;
    }
    .tree-item:hover {
      background: #f0f0f0;
    }
    .tree-item.active {
      background: #E53512;
      color: white;
    }
    .tree-item.subcourse {
      margin-left: 1.5rem;
      font-size: 0.9rem;
    }
    .form-container {
      flex: 1;
      padding: 1rem;
      overflow-y: auto;
    }
    .course-block {
      background: white;
      padding: 1rem;
      margin-bottom: 1rem;
      border-radius: 8px;
      box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
    }
    label {
      font-weight: bold;
      display: block;
      margin-top: 1rem;
    }
    input, textarea {
      width: 100%;
      padding: 0.5rem;
      margin-top: 0.2rem;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    textarea {
      min-height: 100px;
    }
    textarea {
      width: 100%;
      padding: 0.5rem;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-family: inherit;
      font-size: inherit;
      resize: vertical;
      margin-bottom: 1rem;
    }

    textarea:focus {
      outline: none;
      border-color: #E53512;
    }
    .save-btn {
      background: #E53512;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      border-radius: 5px;
      cursor: pointer;
      margin: 1rem;
    }
    .actions {
      padding: 0.5rem 1rem;
      background: #fff;
      border-top: 1px solid #ddd;
    }
    .no-selection {
      display: flex;
      height: 100%;
      align-items: center;
      justify-content: center;
      color: #777;
      font-size: 1.2rem;
    }

    /* Media browser styles */
    .media-browser {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.9);
      z-index: 9999; /* Increased z-index */
      display: none;
      flex-direction: column;
      padding: 2rem;
      box-sizing: border-box;
      color: white;
      overflow: auto; /* Allow scrolling */
    }
    .media-browser.open {
      display: flex;
    }
    .media-browser-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }
    .media-browser-close {
      background: none;
      border: none;
      color: white;
      font-size: 1.5rem;
      cursor: pointer;
    }
    .media-browser-tabs {
      display: flex;
      margin-bottom: 1rem;
    }
    .media-browser-tab {
      padding: 0.5rem 1rem;
      background: #333;
      color: white;
      border: none;
      margin-right: 0.5rem;
      cursor: pointer;
    }
    .media-browser-tab.active {
      background: #E53512;
    }
    .media-browser-content {
      flex: 1;
      overflow-y: auto;
      background: #222;
      padding: 1rem;
      border-radius: 4px;
    }
    .media-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: 1rem;
    }
    .media-item {
      background: #333;
      border-radius: 4px;
      overflow: hidden;
      cursor: pointer;
      position: relative;
      aspect-ratio: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: transform 0.2s;
    }
    .media-item:hover {
      transform: scale(1.05);
    }
    .media-item img {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }
    .media-item.pdf {
      background: #444;
      color: white;
      font-size: 0.8rem;
      text-align: center;
      padding: 0.5rem;
    }
    .media-item.pdf::before {
      content: '📄';
      font-size: 2rem;
      display: block;
      margin-bottom: 0.5rem;
    }
    .media-upload {
      margin-top: 1rem;
      padding: 1rem;
      background: #333;
      border-radius: 4px;
    }
    .media-upload-form {
      display: flex;
      flex-direction: column;
    }
    .media-upload-btn {
      background: #E53512;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;
      margin-top: 0.5rem;
    }
    .media-preview {
      display: flex;
      align-items: center;
      margin-top: 0.5rem;
    }
    .media-preview img {
      max-width: 100px;
      max-height: 100px;
      margin-right: 1rem;
    }
    .field-with-browser {
      display: flex;
      align-items: center;
    }
    .field-with-browser input {
      flex: 1;
    }
    .browse-btn {
      background: #555;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 0 4px 4px 0;
      cursor: pointer;
      margin-left: -1px;
    }
    /* Authentication styles */
    .auth-container {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.9);
      z-index: 1000;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }
    .auth-message {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      text-align: center;
      max-width: 400px;
    }
    .auth-message h2 {
      color: #E53512;
      margin-top: 0;
    }
    .auth-btn {
      background: #E53512;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      border-radius: 5px;
      cursor: pointer;
      margin-top: 1rem;
      text-decoration: none;
      display: inline-block;
    }
    .user-info {
      display: flex;
      align-items: center;
      position: absolute;
      top: 1rem;
      right: 1rem;
    }
    .user-info img {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      margin-right: 0.5rem;
    }
    .logout-btn {
      background: none;
      border: none;
      color: #E53512;
      cursor: pointer;
      margin-left: 1rem;
      text-decoration: underline;
    }

    /* Mobile responsive styles */
    @media (max-width: 768px) {
      body {
        padding: 0;
        min-height: 100vh;
      }

      /* Ensure viewport height is properly calculated on mobile */
      .main-container {
        min-height: calc(100vh - 180px) !important;
      }

      /* Handle mobile keyboard appearance */
      @supports (-webkit-touch-callout: none) {
        .main-container {
          min-height: calc(100vh - 180px) !important;
          height: auto !important;
        }
      }

      h1 {
        font-size: 1.5rem;
        text-align: center;
        margin: 0.5rem;
        padding: 0 1rem;
      }

      .back-link {
        margin: 0.5rem 1rem !important;
        font-size: 0.9rem;
      }

      .lang-tabs {
        padding: 0 1rem;
        margin-bottom: 0.5rem;
      }

      .lang-tabs button {
        margin-right: 0.5rem;
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
      }

      .main-container {
        flex-direction: column;
        height: auto;
        min-height: calc(100vh - 200px);
      }

      .tree-view {
        width: 100%;
        max-height: 30vh;
        min-height: 200px;
        border-right: none;
        border-bottom: 1px solid #ddd;
        flex-shrink: 0;
      }

      .tree-item {
        padding: 0.75rem;
        font-size: 0.9rem;
      }

      .tree-item.subcourse {
        margin-left: 1rem;
        font-size: 0.85rem;
      }

      .form-container {
        padding: 0.5rem;
        flex: 1;
        min-height: 0;
        overflow-y: auto;
      }

      .course-block {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
      }

      .course-block h3 {
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
      }

      input, textarea {
        padding: 0.6rem;
        font-size: 0.9rem;
      }

      label {
        font-size: 0.9rem;
        margin-top: 0.75rem;
      }

      .field-with-browser {
        flex-direction: column;
        align-items: stretch;
      }

      .field-with-browser input {
        margin-bottom: 0.5rem;
        border-radius: 4px;
      }

      .browse-btn {
        border-radius: 4px;
        margin-left: 0;
        padding: 0.6rem 1rem;
      }

      .save-btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
        margin: 0.5rem;
      }

      .actions {
        padding: 0.75rem;
        text-align: center;
      }

      .media-browser {
        padding: 1rem;
      }

      .media-browser-header h2 {
        font-size: 1.3rem;
      }

      .media-browser-tabs {
        flex-wrap: wrap;
        gap: 0.5rem;
      }

      .media-browser-tab {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
      }

      .media-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 0.75rem;
      }

      .media-upload {
        padding: 0.75rem;
      }

      .media-upload h3 {
        font-size: 1.1rem;
      }

      .media-upload-btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
      }

      .user-info {
        position: static;
        justify-content: center;
        margin: 0.5rem;
        padding: 0.5rem;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
      }

      .no-selection {
        font-size: 1rem;
        padding: 2rem 1rem;
        text-align: center;
      }
    }

    @media (max-width: 480px) {
      h1 {
        font-size: 1.3rem;
      }

      .lang-tabs button {
        padding: 0.3rem 0.6rem;
        font-size: 0.8rem;
        margin-right: 0.3rem;
      }

      .tree-view {
        max-height: 25vh;
        min-height: 150px;
      }

      .tree-item {
        padding: 0.5rem;
        font-size: 0.85rem;
      }

      .tree-item.subcourse {
        margin-left: 0.75rem;
        font-size: 0.8rem;
      }

      .course-block {
        padding: 0.5rem;
      }

      .course-block h3 {
        font-size: 1rem;
      }

      input, textarea {
        padding: 0.5rem;
        font-size: 0.85rem;
        /* Ensure input fields are properly visible when focused */
        scroll-margin-top: 100px;
      }

      /* Auto-scroll to focused input on mobile */
      input:focus, textarea:focus, select:focus {
        scroll-behavior: smooth;
      }

      label {
        font-size: 0.85rem;
      }

      .browse-btn {
        padding: 0.5rem 0.8rem;
        font-size: 0.85rem;
      }

      .save-btn {
        padding: 0.5rem 0.8rem;
        font-size: 0.85rem;
      }

      .media-browser {
        padding: 0.5rem;
      }

      .media-browser-header h2 {
        font-size: 1.1rem;
      }

      .media-browser-tab {
        padding: 0.3rem 0.6rem;
        font-size: 0.8rem;
      }

      .media-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 0.5rem;
      }

      .media-upload {
        padding: 0.5rem;
      }

      .media-upload h3 {
        font-size: 1rem;
      }

      .media-upload-btn {
        padding: 0.5rem 0.8rem;
        font-size: 0.85rem;
      }
    }
  </style>
</head>
<body>
  <!-- Authentication overlay -->
  <div class="auth-container" id="auth-container">
    <div class="auth-message">
      <h2>Authentication Required</h2>
      <p>You need to be logged in to access this page.</p>
      <a href="login.html" class="auth-btn">Go to Login</a>
    </div>
  </div>

  <div class="header">
    <div class="header-content">
      <div class="logo-section">
        <img src="assets/picture/snownavi_logo.png" alt="SnowNavi Logo">
        <h1>SnowNavi Admin</h1>
      </div>
      <nav class="nav-links">
        <a href="admin.html" class="nav-link">🏠 Dashboard</a>
        <a href="course_admin.html" class="nav-link" style="color: var(--main-red);">📚 Courses</a>
        <a href="member_admin.html" class="nav-link">👥 Members</a>
        <a href="activity_admin.html" class="nav-link">📅 Activities</a>
        <a href="checkin_admin.html" class="nav-link">✅ Check-ins</a>
        <a href="feedback_template_admin.html" class="nav-link">📝 Templates</a>
      </nav>
      <div class="user-info" id="user-info"></div>
    </div>
  </div>

  <div style="padding: 1rem 2rem;">
    <h2 style="color: var(--main-red); margin: 0 0 1rem 0;">Course Management</h2>
  <div class="lang-tabs">
    <button onclick="switchLang('en')" id="tab-en">English</button>
    <button onclick="switchLang('zh')" id="tab-zh">中文</button>
    <button onclick="switchLang('nl')" id="tab-nl">Nederlands</button>
  </div>

  <div class="main-container">
    <div class="tree-view" id="tree-view"></div>
    <div class="form-container" id="form-container">
      <div class="no-selection">Select a course from the left panel</div>
    </div>
  </div>

    <div class="actions">
      <button class="save-btn" onclick="saveCourses()">Save Changes</button>
    </div>
  </div>

  <!-- Media Browser -->
  <div class="media-browser" id="media-browser">
    <div class="media-browser-header">
      <h2>Media Browser</h2>
      <button class="media-browser-close" onclick="closeMediaBrowser()">&times;</button>
    </div>

    <div class="media-browser-tabs">
      <button class="media-browser-tab active" onclick="switchMediaTab('images')">Images</button>
      <button class="media-browser-tab" onclick="switchMediaTab('pdfs')">PDFs</button>
    </div>

    <div class="media-browser-content" id="media-browser-content">
      <div class="media-grid" id="media-grid">
        <!-- Media items will be loaded here -->
      </div>
    </div>

    <div class="media-upload">
      <h3>Upload New File</h3>
      <form class="media-upload-form" id="upload-form" onsubmit="event.preventDefault();">
        <input type="file" id="file-input" accept=".jpg,.jpeg,.png,.gif,.webp,.pdf" />
        <div class="media-preview" id="media-preview"></div>
        <div class="file-size-info" style="margin-top: 0.5rem; font-size: 0.8rem; color: #777;">
          Maximum file size: 16MB
        </div>
        <button type="button" class="media-upload-btn" onclick="uploadFile()">Upload</button>
      </form>
    </div>
  </div>

  <script>
    let currentLang = 'en';
    let courses = {};
    let selectedCourse = null;
    let selectedSubcourse = null;
    let currentMediaType = 'images';
    let currentField = null;

    // Fetch configuration and check authentication
    async function checkAuth() {
      try {
        // Fetch the authorized email from the server
        const response = await fetch('/api/config');
        if (!response.ok) {
          throw new Error('Failed to fetch configuration');
        }

        const config = await response.json();

        // Parse allowed emails - try new format first, fallback to old format
        let allowedEmails = [];
        if (config.allowedEmails && Array.isArray(config.allowedEmails)) {
          allowedEmails = config.allowedEmails;
        } else if (config.authorizedEmail) {
          allowedEmails = config.authorizedEmail.split(',').map(email => email.trim());
        }

        const auth = JSON.parse(localStorage.getItem('snownavi_auth') || '{}');
        const authContainer = document.getElementById('auth-container');
        const userInfoContainer = document.getElementById('user-info');

        // Check if auth exists, is not expired, and user is authorized
        const isAuthorized = allowedEmails.includes(auth.email);
        if (isAuthorized && auth.expiresAt && auth.expiresAt > Date.now()) {
          // User is authenticated
          authContainer.style.display = 'none';

          // Display user info
          userInfoContainer.innerHTML = `
            <img src="${auth.picture}" alt="Profile">
            <span>${auth.name}</span>
            <button class="logout-btn" onclick="logout()">Logout</button>
          `;

          // Load courses
          loadCourses();
        } else {
          // User is not authenticated, show auth container
          authContainer.style.display = 'flex';
          userInfoContainer.innerHTML = '';
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
        document.getElementById('auth-container').style.display = 'flex';
        document.querySelector('.auth-message p').textContent = 'Error loading configuration. Please try again later.';
      }
    }

    // Logout function
    function logout() {
      localStorage.removeItem('snownavi_auth');
      window.location.href = 'login.html';
    }

    async function loadCourses() {
      const res = await fetch('data/courses.json');
      courses = await res.json();
      document.getElementById(`tab-${currentLang}`).classList.add('active');
      renderTreeView();
    }

    function switchLang(lang) {
      currentLang = lang;
      document.querySelectorAll('.lang-tabs button').forEach(btn => btn.classList.remove('active'));
      document.getElementById(`tab-${lang}`).classList.add('active');
      renderTreeView();
      if (selectedCourse) {
        if (selectedSubcourse !== null) {
          renderSubcourseForm(selectedCourse, selectedSubcourse);
        } else {
          renderCourseForm(selectedCourse);
        }
      }
    }

    function renderTreeView() {
      const treeView = document.getElementById('tree-view');
      treeView.innerHTML = '';

      Object.keys(courses).forEach(courseKey => {
        // Create course item
        const courseItem = document.createElement('div');
        courseItem.className = 'tree-item';
        courseItem.textContent = courses[courseKey][currentLang].title;
        courseItem.dataset.courseKey = courseKey;
        courseItem.onclick = () => selectCourse(courseKey);

        if (selectedCourse === courseKey && selectedSubcourse === null) {
          courseItem.classList.add('active');
        }

        treeView.appendChild(courseItem);

        // Create subcourse items
        courses[courseKey][currentLang].subcourses.forEach((sub, index) => {
          const subItem = document.createElement('div');
          subItem.className = 'tree-item subcourse';
          subItem.textContent = sub.title || `Subcourse ${index + 1}`;
          subItem.dataset.courseKey = courseKey;
          subItem.dataset.subcourseIndex = index;
          subItem.onclick = (e) => {
            e.stopPropagation();
            selectSubcourse(courseKey, index);
          };

          if (selectedCourse === courseKey && selectedSubcourse === index) {
            subItem.classList.add('active');
          }

          treeView.appendChild(subItem);
        });
      });
    }

    function selectCourse(courseKey) {
      selectedCourse = courseKey;
      selectedSubcourse = null;
      renderTreeView();
      renderCourseForm(courseKey);
    }

    function selectSubcourse(courseKey, index) {
      selectedCourse = courseKey;
      selectedSubcourse = index;
      renderTreeView();
      renderSubcourseForm(courseKey, index);
    }

    function renderCourseForm(courseKey) {
      const container = document.getElementById('form-container');
      container.innerHTML = '';

      const course = courses[courseKey][currentLang];
      const div = document.createElement('div');
      div.className = 'course-block';

      // Course title
      const titleHeader = document.createElement('h2');
      titleHeader.textContent = `${courseKey}`;
      div.appendChild(titleHeader);

      // Course title input
      const titleLabel = document.createElement('label');
      titleLabel.textContent = 'Course Title';
      div.appendChild(titleLabel);

      const titleInput = document.createElement('input');
      titleInput.value = course.title;
      titleInput.oninput = e => course.title = e.target.value;
      div.appendChild(titleInput);

      // Course description textarea
      const descLabel = document.createElement('label');
      descLabel.textContent = 'Course Description (Optional)';
      div.appendChild(descLabel);

      const descInput = document.createElement('textarea');
      descInput.value = course.description || '';
      descInput.oninput = e => course.description = e.target.value;
      descInput.style.minHeight = '100px';
      descInput.placeholder = 'Enter course description (optional)';
      div.appendChild(descInput);

      // Add subcourse button
      const addBtn = document.createElement('button');
      addBtn.textContent = 'Add New Subcourse';
      addBtn.className = 'save-btn';
      addBtn.style.marginTop = '1rem';
      addBtn.onclick = () => addNewSubcourse(courseKey);
      div.appendChild(addBtn);

      container.appendChild(div);
    }
    // Media Browser Functions
    function openMediaBrowser(fieldName, mediaType = 'images') {
      currentField = fieldName;
      currentMediaType = mediaType;

      try {
        // Update active tab
        document.querySelectorAll('.media-browser-tab').forEach(tab => tab.classList.remove('active'));
        const tab = document.querySelector(`.media-browser-tab[onclick*="'${mediaType}'"]`);
        if (tab) {
          tab.classList.add('active');
        }

        // Clear file input
        const fileInput = document.getElementById('file-input');
        if (fileInput) {
          fileInput.value = '';
        }

        const preview = document.getElementById('media-preview');
        if (preview) {
          preview.innerHTML = '';
        }

        // Show browser
        const mediaBrowser = document.getElementById('media-browser');
        if (mediaBrowser) {
          mediaBrowser.classList.add('open');
          mediaBrowser.style.display = 'flex';

          // Load media files
          loadMediaFiles(mediaType);
        }
      } catch (error) {
        console.error('Error opening media browser:', error);
      }
    }

    function closeMediaBrowser() {
      const mediaBrowser = document.getElementById('media-browser');
      if (mediaBrowser) {
        mediaBrowser.classList.remove('open');
        mediaBrowser.style.display = 'none';
      }
    }

    function switchMediaTab(mediaType) {
      currentMediaType = mediaType;

      try {
        // Update active tab
        document.querySelectorAll('.media-browser-tab').forEach(tab => tab.classList.remove('active'));
        const tab = document.querySelector(`.media-browser-tab[onclick*="'${mediaType}'"]`);
        if (tab) {
          tab.classList.add('active');
        }

        // Update file input accept attribute
        const fileInput = document.getElementById('file-input');
        if (fileInput) {
          fileInput.accept = mediaType === 'images' ? '.jpg,.jpeg,.png,.gif,.webp' : '.pdf';
        }

        // Load media files
        loadMediaFiles(mediaType);
      } catch (error) {
        console.error('Error switching media tab:', error);
      }
    }

    async function loadMediaFiles(mediaType) {
      try {
        const response = await fetch(`/api/files/${mediaType}`);

        if (!response.ok) {
          throw new Error(`Failed to load media files: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        renderMediaGrid(data.files, mediaType);
      } catch (error) {
        console.error('Error loading media files:', error);
        document.getElementById('media-grid').innerHTML = `<p>Error loading files: ${error.message}</p>`;
      }
    }

    function renderMediaGrid(files, mediaType) {
      const grid = document.getElementById('media-grid');
      grid.innerHTML = '';

      if (files.length === 0) {
        grid.innerHTML = `<p>No ${mediaType} found. Upload some files below.</p>`;
        return;
      }

      files.forEach(file => {
        const item = document.createElement('div');
        item.className = `media-item ${mediaType === 'pdfs' ? 'pdf' : ''}`;
        item.onclick = () => selectMedia(file.url);

        if (mediaType === 'images') {
          const img = document.createElement('img');
          img.src = file.url;
          img.alt = file.name;
          item.appendChild(img);
        } else {
          // For PDFs, just show the filename
          item.textContent = file.name.length > 20 ? file.name.substring(0, 17) + '...' : file.name;
        }

        grid.appendChild(item);
      });
    }

    function selectMedia(url) {
      if (currentField) {
        // Find the input field and update its value
        const input = document.querySelector(`input[data-field="${currentField}"]`);
        if (input) {
          input.value = url;
          // Trigger the oninput event to update the data
          const event = new Event('input', { bubbles: true });
          input.dispatchEvent(event);
        }
      }

      closeMediaBrowser();
    }

    // Helper function to format file size
    function formatFileSize(bytes) {
      if (bytes < 1024) {
        return bytes + ' bytes';
      } else if (bytes < 1024 * 1024) {
        return (bytes / 1024).toFixed(1) + ' KB';
      } else {
        return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
      }
    }

    // File upload handling
    document.getElementById('file-input').addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (!file) return;

      const preview = document.getElementById('media-preview');
      preview.innerHTML = '';

      // Show file size
      const sizeInfo = document.createElement('div');
      sizeInfo.textContent = `File size: ${formatFileSize(file.size)}`;
      sizeInfo.style.marginBottom = '0.5rem';
      sizeInfo.style.fontSize = '0.9rem';

      // Add warning if file is too large
      const maxSizeBytes = 16 * 1024 * 1024;
      if (file.size > maxSizeBytes) {
        sizeInfo.style.color = '#ff4444';
        sizeInfo.style.fontWeight = 'bold';
      }

      preview.appendChild(sizeInfo);

      if (file.type.startsWith('image/')) {
        const img = document.createElement('img');
        img.src = URL.createObjectURL(file);
        preview.appendChild(img);
      } else if (file.type === 'application/pdf') {
        const div = document.createElement('div');
        div.textContent = `PDF: ${file.name}`;
        div.style.padding = '0.5rem';
        div.style.background = '#444';
        div.style.borderRadius = '4px';
        preview.appendChild(div);
      }
    });

    async function uploadFile() {
      const fileInput = document.getElementById('file-input');
      const file = fileInput.files[0];

      if (!file) {
        alert('Please select a file to upload');
        return;
      }

      // Create form data
      const formData = new FormData();
      formData.append('file', file);

      try {
        // Check file size before uploading
        const maxSizeMB = 16; // Match the server-side limit
        const maxSizeBytes = maxSizeMB * 1024 * 1024;

        if (file.size > maxSizeBytes) {
          throw new Error(`File too large. Maximum allowed size is ${maxSizeMB}MB. Your file is ${(file.size / (1024 * 1024)).toFixed(1)}MB.`);
        }

        const response = await fetch(`/api/upload/${currentMediaType}`, {
          method: 'POST',
          body: formData
        });

        // Handle 413 error specifically
        if (response.status === 413) {
          throw new Error(`File too large. Maximum allowed size is ${maxSizeMB}MB.`);
        }

        // Check if the response is JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          // Not a JSON response, try to get the text
          const text = await response.text();
          throw new Error(`Server returned non-JSON response: ${text.substring(0, 100)}...`);
        }

        // Parse the JSON response
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Upload failed');
        }

        // Reload media files
        loadMediaFiles(currentMediaType);

        // Clear file input and preview
        fileInput.value = '';
        document.getElementById('media-preview').innerHTML = '';

        // Select the uploaded file if needed
        if (confirm('File uploaded successfully! Do you want to use this file?')) {
          selectMedia(data.url);
        }
      } catch (error) {
        console.error('Error uploading file:', error);
        alert(`Error uploading file: ${error.message}`);
      }
    }

    function renderSubcourseForm(courseKey, index) {
      const container = document.getElementById('form-container');
      container.innerHTML = '';

      const course = courses[courseKey][currentLang];
      const sub = course.subcourses[index];

      const div = document.createElement('div');
      div.className = 'course-block';

      // Subcourse header
      const header = document.createElement('h2');
      header.textContent = `${courseKey} - ${sub.title || `Subcourse ${index + 1}`}`;
      div.appendChild(header);

      // Form fields
      ['title', 'time', 'location', 'price', 'desc'].forEach(field => {
        const label = document.createElement('label');
        label.textContent = field.charAt(0).toUpperCase() + field.slice(1);
        div.appendChild(label);

        const input = field === 'desc' ? document.createElement('textarea') : document.createElement('input');
        input.value = sub[field] || '';
        input.oninput = e => sub[field] = e.target.value;
        div.appendChild(input);
      });

      // Image field with browser button
      const imageLabel = document.createElement('label');
      imageLabel.textContent = 'Image';
      div.appendChild(imageLabel);

      const imageFieldContainer = document.createElement('div');
      imageFieldContainer.className = 'field-with-browser';

      const imageInput = document.createElement('input');
      imageInput.value = sub.image || '';
      imageInput.oninput = e => sub.image = e.target.value;
      imageInput.dataset.field = 'image';
      imageFieldContainer.appendChild(imageInput);

      const imageBrowseBtn = document.createElement('button');
      imageBrowseBtn.textContent = 'Browse';
      imageBrowseBtn.className = 'browse-btn';
      imageBrowseBtn.type = 'button'; // Prevent form submission
      imageBrowseBtn.onclick = (e) => {
        e.preventDefault();
        openMediaBrowser('image', 'images');
      };
      imageFieldContainer.appendChild(imageBrowseBtn);

      div.appendChild(imageFieldContainer);

      // Preview current image if exists
      if (sub.image) {
        const imagePreview = document.createElement('img');
        imagePreview.src = sub.image;
        imagePreview.alt = 'Preview';
        imagePreview.style.maxWidth = '200px';
        imagePreview.style.maxHeight = '200px';
        imagePreview.style.marginTop = '0.5rem';
        imagePreview.style.border = '1px solid #ddd';
        div.appendChild(imagePreview);
      }

      // PDF field with browser button
      const pdfLabel = document.createElement('label');
      pdfLabel.textContent = 'PDF';
      div.appendChild(pdfLabel);

      const pdfFieldContainer = document.createElement('div');
      pdfFieldContainer.className = 'field-with-browser';

      const pdfInput = document.createElement('input');
      pdfInput.value = sub.pdf || '';
      pdfInput.oninput = e => sub.pdf = e.target.value;
      pdfInput.dataset.field = 'pdf';
      pdfFieldContainer.appendChild(pdfInput);

      const pdfBrowseBtn = document.createElement('button');
      pdfBrowseBtn.textContent = 'Browse';
      pdfBrowseBtn.className = 'browse-btn';
      pdfBrowseBtn.type = 'button'; // Prevent form submission
      pdfBrowseBtn.onclick = (e) => {
        e.preventDefault();
        openMediaBrowser('pdf', 'pdfs');
      };
      pdfFieldContainer.appendChild(pdfBrowseBtn);

      div.appendChild(pdfFieldContainer);

      // Preview current PDF if exists
      if (sub.pdf) {
        const pdfLink = document.createElement('a');
        pdfLink.href = sub.pdf;
        pdfLink.target = '_blank';
        pdfLink.textContent = 'View PDF';
        pdfLink.style.display = 'inline-block';
        pdfLink.style.marginTop = '0.5rem';
        div.appendChild(pdfLink);
      }

      // Delete button
      const deleteBtn = document.createElement('button');
      deleteBtn.textContent = 'Delete Subcourse';
      deleteBtn.style.backgroundColor = '#ff4444';
      deleteBtn.className = 'save-btn';
      deleteBtn.style.marginTop = '2rem';
      deleteBtn.onclick = () => deleteSubcourse(courseKey, index);
      div.appendChild(deleteBtn);

      container.appendChild(div);
    }

    function addNewSubcourse(courseKey) {
      const newSubcourse = {
        title: 'New Subcourse',
        time: '',
        location: '',
        price: '',
        desc: '',
        image: '',
        pdf: ''
      };

      courses[courseKey][currentLang].subcourses.push(newSubcourse);
      renderTreeView();
      selectSubcourse(courseKey, courses[courseKey][currentLang].subcourses.length - 1);
    }

    function deleteSubcourse(courseKey, index) {
      if (confirm('Are you sure you want to delete this subcourse?')) {
        courses[courseKey][currentLang].subcourses.splice(index, 1);
        selectedSubcourse = null;
        renderTreeView();
        selectCourse(courseKey);
      }
    }

    function saveCourses() {
      fetch('data/courses.json', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(courses, null, 2)
      }).then(res => {
        if (res.ok) alert('Saved successfully!');
        else alert('Error saving file');
      });
    }

    // Initialize the page
    window.onload = checkAuth;
  </script>
</body>
</html>
