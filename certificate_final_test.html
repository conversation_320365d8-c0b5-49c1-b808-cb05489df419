<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate Final Test - SnowNavi</title>
    <style>
        :root {
            --main-red: #E53512;
            --bg-light: #F9F4F3;
            --text-dark: #2F2F2F;
            --text-gray: #717171;
            --contrast-white: #FFFFFF;
            --accent-blue: #9ED4E7;
        }

        body { 
            font-family: 'Noto Sans SC', sans-serif; 
            margin: 2rem; 
            background: var(--bg-light);
            color: var(--text-dark);
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--contrast-white);
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 3px solid var(--main-red);
        }
        h1 { color: var(--main-red); }
        h2 { color: var(--text-dark); border-bottom: 2px solid var(--main-red); padding-bottom: 0.5rem; }
        .test-section { 
            margin: 2rem 0; 
            padding: 1.5rem; 
            border: 1px solid rgba(229, 53, 18, 0.1);
            border-radius: 8px;
            background: var(--bg-light);
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 0.75rem;
            margin: 0.5rem 0;
            background: var(--contrast-white);
            border-radius: 6px;
            border-left: 4px solid var(--accent-blue);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        .checklist li.completed {
            border-left-color: #28a745;
            background: #f8fff9;
        }
        .checklist li.error {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        .check-icon {
            font-size: 1.2rem;
            width: 24px;
            text-align: center;
        }
        .test-result {
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
            font-weight: bold;
        }
        .test-result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-result.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .btn {
            background: var(--main-red);
            color: var(--contrast-white);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 0.5rem;
        }
        .btn:hover {
            background: #c42e0f;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(229, 53, 18, 0.3);
        }
        .btn:disabled {
            background: var(--text-gray);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .portal-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        .portal-link {
            background: var(--contrast-white);
            padding: 1rem;
            border-radius: 6px;
            text-align: center;
            border: 1px solid rgba(229, 53, 18, 0.2);
            transition: transform 0.2s;
        }
        .portal-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .portal-link a {
            color: var(--main-red);
            text-decoration: none;
            font-weight: bold;
        }
        .portal-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Certificate System Final Test</h1>
            <p>Comprehensive testing of the enhanced certificate system with authentic SnowNavi branding</p>
        </div>
        
        <div class="test-section">
            <h2>🔍 System Verification</h2>
            <button class="btn" onclick="runSystemTests()">Run All Tests</button>
            
            <ul class="checklist" id="system-checklist">
                <li id="test-images">
                    <span class="check-icon">⏳</span>
                    <span>Loading SnowNavi logo and QR code images</span>
                </li>
                <li id="test-generator">
                    <span class="check-icon">⏳</span>
                    <span>Certificate generator initialization</span>
                </li>
                <li id="test-canvas">
                    <span class="check-icon">⏳</span>
                    <span>Canvas rendering capabilities</span>
                </li>
                <li id="test-download">
                    <span class="check-icon">⏳</span>
                    <span>JPG download functionality</span>
                </li>
                <li id="test-qr">
                    <span class="check-icon">⏳</span>
                    <span>QR code generation (member, WeChat, Xiaohongshu)</span>
                </li>
            </ul>
            
            <div id="test-results"></div>
        </div>
        
        <div class="test-section">
            <h2>📋 Manual Testing Checklist</h2>
            
            <h3>Visual Elements</h3>
            <ul class="checklist">
                <li>
                    <span class="check-icon">📋</span>
                    <span>SnowNavi logo appears correctly in certificate header</span>
                </li>
                <li>
                    <span class="check-icon">📋</span>
                    <span>Official brand colors (#E53512, #F9F4F3, #9ED4E7) are used</span>
                </li>
                <li>
                    <span class="check-icon">📋</span>
                    <span>3:4 aspect ratio (1200x1600px) is maintained</span>
                </li>
                <li>
                    <span class="check-icon">📋</span>
                    <span>Professional typography and layout spacing</span>
                </li>
            </ul>
            
            <h3>QR Code Integration</h3>
            <ul class="checklist">
                <li>
                    <span class="check-icon">📋</span>
                    <span>Member verification QR code is unique per member</span>
                </li>
                <li>
                    <span class="check-icon">📋</span>
                    <span>WeChat QR code loads from wechat_qrcode.jpg</span>
                </li>
                <li>
                    <span class="check-icon">📋</span>
                    <span>Xiaohongshu QR code loads from xiaohongshu_qrcode.jpg</span>
                </li>
                <li>
                    <span class="check-icon">📋</span>
                    <span>QR codes are properly sized and positioned</span>
                </li>
            </ul>
            
            <h3>Content Accuracy</h3>
            <ul class="checklist">
                <li>
                    <span class="check-icon">📋</span>
                    <span>Student name and member ID display correctly</span>
                </li>
                <li>
                    <span class="check-icon">📋</span>
                    <span>Course name and date are accurate</span>
                </li>
                <li>
                    <span class="check-icon">📋</span>
                    <span>Skill progress bars reflect actual assessment data</span>
                </li>
                <li>
                    <span class="check-icon">📋</span>
                    <span>Contact information (email, website) is correct</span>
                </li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2>🚀 Integration Testing</h2>
            
            <div class="portal-links">
                <div class="portal-link">
                    <h4>Certificate Generator</h4>
                    <a href="test_certificate.html" target="_blank">Test Generator</a>
                    <p>Test certificate generation with custom parameters</p>
                </div>
                
                <div class="portal-link">
                    <h4>Student Portal</h4>
                    <a href="student_feedback.html" target="_blank">Student Portal</a>
                    <p>Test end-to-end certificate download</p>
                </div>
                
                <div class="portal-link">
                    <h4>Design Showcase</h4>
                    <a href="certificate_design_showcase.html" target="_blank">Design Showcase</a>
                    <p>View design improvements and features</p>
                </div>
                
                <div class="portal-link">
                    <h4>Feature Demo</h4>
                    <a href="demo_certificate.html" target="_blank">Feature Demo</a>
                    <p>Complete feature demonstration</p>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📝 Test Scenarios</h2>
            
            <h3>Scenario 1: Beginner Student</h3>
            <p><strong>Test Data:</strong> <EMAIL>, Basic + some Sliding skills</p>
            <p><strong>Expected:</strong> Certificate shows partial progress, authentic branding</p>
            
            <h3>Scenario 2: Advanced Student</h3>
            <p><strong>Test Data:</strong> <EMAIL>, Multiple skill sections completed</p>
            <p><strong>Expected:</strong> Certificate shows high progress, all QR codes visible</p>
            
            <h3>Scenario 3: Custom Test</h3>
            <p><strong>Test Data:</strong> Use test_certificate.html with custom parameters</p>
            <p><strong>Expected:</strong> All elements render correctly, download works</p>
        </div>
    </div>

    <script src="certificate-generator.js"></script>
    <script>
        let certificateGenerator = null;
        
        async function runSystemTests() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<div class="test-result warning">🔄 Running system tests...</div>';
            
            try {
                // Initialize generator
                certificateGenerator = new CertificateGenerator();
                updateTestStatus('test-generator', true, 'Certificate generator initialized');
                
                // Test image loading
                await testImageLoading();
                
                // Test canvas capabilities
                testCanvasCapabilities();
                
                // Test QR generation
                testQRGeneration();
                
                // Test download functionality
                await testDownloadFunctionality();
                
                results.innerHTML = '<div class="test-result success">✅ All system tests passed! Certificate system is ready for use.</div>';
                
            } catch (error) {
                console.error('System test failed:', error);
                results.innerHTML = `<div class="test-result error">❌ System test failed: ${error.message}</div>`;
            }
        }
        
        async function testImageLoading() {
            try {
                await certificateGenerator.preloadImages();
                
                // Check if images loaded
                const logo = await certificateGenerator.loadImage('assets/picture/snownavi_logo.png');
                const wechatQR = await certificateGenerator.loadImage('assets/picture/wechat_qrcode.jpg');
                const xhsQR = await certificateGenerator.loadImage('assets/picture/xiaohongshu_qrcode.jpg');
                
                if (logo && wechatQR && xhsQR) {
                    updateTestStatus('test-images', true, 'All images loaded successfully');
                } else {
                    updateTestStatus('test-images', false, 'Some images failed to load');
                }
            } catch (error) {
                updateTestStatus('test-images', false, `Image loading failed: ${error.message}`);
            }
        }
        
        function testCanvasCapabilities() {
            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                if (ctx && typeof ctx.fillText === 'function' && typeof ctx.drawImage === 'function') {
                    updateTestStatus('test-canvas', true, 'Canvas rendering capabilities confirmed');
                } else {
                    updateTestStatus('test-canvas', false, 'Canvas capabilities insufficient');
                }
            } catch (error) {
                updateTestStatus('test-canvas', false, `Canvas test failed: ${error.message}`);
            }
        }
        
        function testQRGeneration() {
            try {
                const memberQR = certificateGenerator.generateMemberQRPattern('SN20250001', 100);
                
                if (memberQR && memberQR.width === 100 && memberQR.height === 100) {
                    updateTestStatus('test-qr', true, 'QR code generation working');
                } else {
                    updateTestStatus('test-qr', false, 'QR code generation failed');
                }
            } catch (error) {
                updateTestStatus('test-qr', false, `QR generation failed: ${error.message}`);
            }
        }
        
        async function testDownloadFunctionality() {
            try {
                const testData = {
                    memberName: 'Test User',
                    memberId: 'SN20250001',
                    activityName: 'Test Course',
                    activityDate: '2025-01-27',
                    feedback: {
                        skillAssessment: {
                            completedSkills: ['equipment-intro', 'single-foot-familiarity'],
                            sectionFeedbacks: { basic: 'Test feedback' }
                        }
                    }
                };
                
                const canvas = await certificateGenerator.generateAdvancedCertificate(testData);
                
                if (canvas && canvas.width === 1200 && canvas.height === 1600) {
                    updateTestStatus('test-download', true, 'Certificate generation and download ready');
                } else {
                    updateTestStatus('test-download', false, 'Certificate generation failed');
                }
            } catch (error) {
                updateTestStatus('test-download', false, `Download test failed: ${error.message}`);
            }
        }
        
        function updateTestStatus(testId, success, message) {
            const testElement = document.getElementById(testId);
            const icon = testElement.querySelector('.check-icon');
            const text = testElement.querySelector('span:last-child');
            
            if (success) {
                icon.textContent = '✅';
                testElement.classList.add('completed');
                text.textContent = message;
            } else {
                icon.textContent = '❌';
                testElement.classList.add('error');
                text.textContent = message;
            }
        }
        
        // Auto-run tests on page load
        window.onload = () => {
            setTimeout(runSystemTests, 1000);
        };
    </script>
</body>
</html>
