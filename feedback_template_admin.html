<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feedback Template Management - Snow<PERSON>avi <PERSON>min</title>
    <link rel="icon" type="image/png" href="assets/picture/snownavi_logo.png">
    <style>
        :root {
            --main-red: #E53512;
            --bg-light: #F9F4F3;
            --text-dark: #2F2F2F;
            --text-gray: #717171;
            --contrast-white: #FFFFFF;
            --accent-blue: #9ED4E7;
            --success-green: #28a745;
            --warning-orange: #ffc107;
            --danger-red: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--bg-light);
            color: var(--text-dark);
            line-height: 1.6;
        }

        .header {
            background: var(--contrast-white);
            padding: 1rem 2rem;
            border-bottom: 3px solid var(--main-red);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo-section img {
            width: 50px;
            height: 50px;
        }

        .logo-section h1 {
            color: var(--main-red);
            font-size: 1.8rem;
        }

        .nav-links {
            display: flex;
            gap: 1rem;
        }

        .nav-link {
            color: var(--text-gray);
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .nav-link:hover {
            background: var(--bg-light);
            color: var(--main-red);
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        .page-title {
            text-align: center;
            margin-bottom: 2rem;
        }

        .page-title h2 {
            color: var(--main-red);
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .page-title p {
            color: var(--text-gray);
            font-size: 1.1rem;
        }

        .main-container {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 2rem;
            min-height: 600px;
        }

        .template-list {
            background: var(--contrast-white);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            height: fit-content;
        }

        .template-list h3 {
            color: var(--main-red);
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .template-item {
            padding: 1rem;
            border: 2px solid transparent;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
            background: var(--bg-light);
        }

        .template-item:hover {
            border-color: var(--accent-blue);
            transform: translateY(-2px);
        }

        .template-item.active {
            border-color: var(--main-red);
            background: rgba(229, 53, 18, 0.1);
        }

        .template-item h4 {
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }

        .template-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
            color: var(--text-gray);
        }

        .template-badge {
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .badge-beginner {
            background: var(--success-green);
            color: white;
        }

        .badge-intermediate {
            background: var(--warning-orange);
            color: white;
        }

        .badge-advanced {
            background: var(--danger-red);
            color: white;
        }

        .badge-snowboard {
            background: var(--accent-blue);
            color: white;
        }

        .badge-ski {
            background: var(--main-red);
            color: white;
        }

        .form-container {
            background: var(--contrast-white);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .no-selection {
            text-align: center;
            color: var(--text-gray);
            font-size: 1.2rem;
            padding: 3rem;
        }

        .form-section {
            margin-bottom: 2rem;
        }

        .form-section h3 {
            color: var(--main-red);
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: var(--text-dark);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.2s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--main-red);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 2rem;
            padding: 1rem 2rem;
            background: var(--contrast-white);
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: var(--main-red);
            color: var(--contrast-white);
        }

        .btn-primary:hover {
            background: #c42e0f;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(229, 53, 18, 0.3);
        }

        .btn-secondary {
            background: var(--text-gray);
            color: var(--contrast-white);
        }

        .btn-secondary:hover {
            background: #5a5a5a;
            transform: translateY(-2px);
        }

        .btn-success {
            background: var(--success-green);
            color: var(--contrast-white);
        }

        .btn-success:hover {
            background: #218838;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: var(--danger-red);
            color: var(--contrast-white);
        }

        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-2px);
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: var(--text-gray);
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
            border: 1px solid #f5c6cb;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
            border: 1px solid #c3e6cb;
        }

        @media (max-width: 768px) {
            .main-container {
                grid-template-columns: 1fr;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo-section">
                <img src="assets/picture/snownavi_logo.png" alt="SnowNavi Logo">
                <h1>SnowNavi Admin</h1>
            </div>
            <nav class="nav-links">
                <a href="admin.html" class="nav-link">🏠 Dashboard</a>
                <a href="course_admin.html" class="nav-link">📚 Courses</a>
                <a href="member_admin.html" class="nav-link">👥 Members</a>
                <a href="activity_admin.html" class="nav-link">📅 Activities</a>
                <a href="checkin_admin.html" class="nav-link">✅ Check-ins</a>
                <a href="feedback_template_admin.html" class="nav-link" style="color: var(--main-red);">📝 Templates</a>
            </nav>
        </div>
    </div>

    <div class="container">
        <div class="page-title">
            <h2>📝 Feedback Template Management</h2>
            <p>Create and manage feedback templates for different course types and skill levels</p>
        </div>

        <div class="main-container">
            <div class="template-list" id="template-list">
                <h3>📋 Available Templates</h3>
                <div class="loading">Loading templates...</div>
            </div>
            
            <div class="form-container" id="form-container">
                <div class="no-selection">Select a template from the left panel or create a new one</div>
            </div>
        </div>

        <div class="actions">
            <button class="btn btn-primary" onclick="addNewTemplate()">➕ Add New Template</button>
            <div style="display: flex; gap: 1rem;">
                <button class="btn btn-success" onclick="saveTemplates()">💾 Save Changes</button>
                <button class="btn btn-secondary" onclick="previewTemplate()">👁️ Preview</button>
                <button class="btn btn-danger" onclick="deleteTemplate()" id="delete-btn" style="display: none;">🗑️ Delete</button>
            </div>
        </div>
    </div>

    <script>
        let templates = {};
        let selectedTemplate = null;
        let currentLang = 'en';

        // Initialize the page
        async function init() {
            try {
                await loadTemplates();
                renderTemplateList();
            } catch (error) {
                console.error('Error initializing page:', error);
                showError('Failed to load templates. Please try again later.');
            }
        }

        // Load templates from server
        async function loadTemplates() {
            try {
                const response = await fetch('data/feedback_templates.json');
                if (!response.ok) {
                    throw new Error('Failed to load templates');
                }
                templates = await response.json();
                console.log('Loaded templates:', templates);
            } catch (error) {
                console.error('Error loading templates:', error);
                templates = {};
            }
        }

        // Render template list
        function renderTemplateList() {
            const templateList = document.getElementById('template-list');
            const templateContainer = templateList.querySelector('.loading') ? 
                templateList : templateList.querySelector('.template-container');
            
            if (Object.keys(templates).length === 0) {
                templateList.innerHTML = `
                    <h3>📋 Available Templates</h3>
                    <div style="text-align: center; color: var(--text-gray); padding: 2rem;">
                        No templates found. Create your first template!
                    </div>
                `;
                return;
            }

            let html = '<h3>📋 Available Templates</h3>';
            
            Object.entries(templates).forEach(([templateId, template]) => {
                const templateName = template.name[currentLang] || template.name.en;
                const templateDesc = template.description[currentLang] || template.description.en;
                
                html += `
                    <div class="template-item ${selectedTemplate === templateId ? 'active' : ''}" 
                         onclick="selectTemplate('${templateId}')" data-template-id="${templateId}">
                        <h4>${templateName}</h4>
                        <p style="font-size: 0.9rem; color: var(--text-gray); margin-bottom: 0.5rem;">
                            ${templateDesc.substring(0, 80)}${templateDesc.length > 80 ? '...' : ''}
                        </p>
                        <div class="template-meta">
                            <div>
                                <span class="template-badge badge-${template.targetLevel}">${template.targetLevel}</span>
                                <span class="template-badge badge-${template.sport}">${template.sport}</span>
                            </div>
                            <div style="font-size: 0.8rem;">
                                ${Object.keys(template.sections || {}).length} sections
                            </div>
                        </div>
                    </div>
                `;
            });

            templateList.innerHTML = html;
        }

        // Select a template
        function selectTemplate(templateId) {
            selectedTemplate = templateId;
            renderTemplateList();
            renderTemplateForm(templateId);
            document.getElementById('delete-btn').style.display = 'inline-block';
        }

        // Show error message
        function showError(message) {
            const container = document.getElementById('form-container');
            container.innerHTML = `<div class="error">${message}</div>`;
        }

        // Show success message
        function showSuccess(message) {
            const container = document.getElementById('form-container');
            const existingContent = container.innerHTML;
            container.innerHTML = `<div class="success">${message}</div>` + existingContent;
            setTimeout(() => {
                const successDiv = container.querySelector('.success');
                if (successDiv) successDiv.remove();
            }, 3000);
        }

        // Render template form
        function renderTemplateForm(templateId) {
            const template = templates[templateId];
            const formContainer = document.getElementById('form-container');

            if (!template) {
                formContainer.innerHTML = '<div class="no-selection">Template not found</div>';
                return;
            }

            const html = `
                <div class="form-section">
                    <h3>📝 Template Information</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="template-id">Template ID</label>
                            <input type="text" id="template-id" value="${template.id}" readonly style="background: #f5f5f5;">
                        </div>
                        <div class="form-group">
                            <label for="template-sport">Sport</label>
                            <select id="template-sport">
                                <option value="snowboard" ${template.sport === 'snowboard' ? 'selected' : ''}>Snowboard</option>
                                <option value="ski" ${template.sport === 'ski' ? 'selected' : ''}>Ski</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="template-level">Target Level</label>
                            <select id="template-level">
                                <option value="beginner" ${template.targetLevel === 'beginner' ? 'selected' : ''}>Beginner</option>
                                <option value="intermediate" ${template.targetLevel === 'intermediate' ? 'selected' : ''}>Intermediate</option>
                                <option value="advanced" ${template.targetLevel === 'advanced' ? 'selected' : ''}>Advanced</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="template-version">Version</label>
                            <input type="text" id="template-version" value="${template.version || '1.0'}">
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h3>🌐 Template Names</h3>
                    <div class="form-group">
                        <label for="name-en">Name (English)</label>
                        <input type="text" id="name-en" value="${template.name.en || ''}" placeholder="Template name in English">
                    </div>
                    <div class="form-group">
                        <label for="name-zh">Name (中文)</label>
                        <input type="text" id="name-zh" value="${template.name.zh || ''}" placeholder="Template name in Chinese">
                    </div>
                    <div class="form-group">
                        <label for="name-nl">Name (Nederlands)</label>
                        <input type="text" id="name-nl" value="${template.name.nl || ''}" placeholder="Template name in Dutch">
                    </div>
                </div>

                <div class="form-section">
                    <h3>📄 Template Descriptions</h3>
                    <div class="form-group">
                        <label for="desc-en">Description (English)</label>
                        <textarea id="desc-en" placeholder="Template description in English">${template.description.en || ''}</textarea>
                    </div>
                    <div class="form-group">
                        <label for="desc-zh">Description (中文)</label>
                        <textarea id="desc-zh" placeholder="Template description in Chinese">${template.description.zh || ''}</textarea>
                    </div>
                    <div class="form-group">
                        <label for="desc-nl">Description (Nederlands)</label>
                        <textarea id="desc-nl" placeholder="Template description in Dutch">${template.description.nl || ''}</textarea>
                    </div>
                </div>

                <div class="form-section">
                    <h3>🎯 Skill Sections</h3>
                    <div id="sections-container">
                        ${renderSectionsForm(template.sections || {})}
                    </div>
                    <button type="button" class="btn btn-secondary" onclick="addNewSection()">➕ Add Section</button>
                </div>
            `;

            formContainer.innerHTML = html;
        }

        // Render sections form
        function renderSectionsForm(sections) {
            let html = '';

            Object.entries(sections).forEach(([sectionId, section]) => {
                html += `
                    <div class="section-item" data-section-id="${sectionId}" style="border: 2px solid #ddd; border-radius: 8px; padding: 1rem; margin-bottom: 1rem;">
                        <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 1rem;">
                            <h4 style="color: var(--main-red);">Section: ${sectionId}</h4>
                            <button type="button" class="btn btn-danger" onclick="removeSection('${sectionId}')" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">Remove</button>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>Section Order</label>
                                <input type="number" value="${section.order || 1}" onchange="updateSectionOrder('${sectionId}', this.value)">
                            </div>
                            <div class="form-group">
                                <label>Section ID</label>
                                <input type="text" value="${sectionId}" onchange="updateSectionId('${sectionId}', this.value)">
                            </div>
                        </div>

                        <div class="form-group">
                            <label>Title (English)</label>
                            <input type="text" value="${section.title.en || ''}" onchange="updateSectionTitle('${sectionId}', 'en', this.value)">
                        </div>
                        <div class="form-group">
                            <label>Title (中文)</label>
                            <input type="text" value="${section.title.zh || ''}" onchange="updateSectionTitle('${sectionId}', 'zh', this.value)">
                        </div>
                        <div class="form-group">
                            <label>Title (Nederlands)</label>
                            <input type="text" value="${section.title.nl || ''}" onchange="updateSectionTitle('${sectionId}', 'nl', this.value)">
                        </div>

                        <div style="margin-top: 1rem;">
                            <h5 style="color: var(--text-dark); margin-bottom: 0.5rem;">Skills in this section:</h5>
                            <div id="skills-${sectionId}">
                                ${renderSkillsForm(sectionId, section.skills || {})}
                            </div>
                            <button type="button" class="btn btn-secondary" onclick="addNewSkill('${sectionId}')" style="padding: 0.5rem; font-size: 0.9rem;">➕ Add Skill</button>
                        </div>
                    </div>
                `;
            });

            return html;
        }

        // Render skills form
        function renderSkillsForm(sectionId, skills) {
            let html = '';

            Object.entries(skills).forEach(([skillId, skill]) => {
                html += `
                    <div class="skill-item" data-skill-id="${skillId}" style="border: 1px solid #ccc; border-radius: 4px; padding: 0.75rem; margin-bottom: 0.5rem; background: var(--bg-light);">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                            <strong style="color: var(--text-dark);">Skill: ${skillId}</strong>
                            <button type="button" class="btn btn-danger" onclick="removeSkill('${sectionId}', '${skillId}')" style="padding: 0.2rem 0.4rem; font-size: 0.7rem;">Remove</button>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>Skill Order</label>
                                <input type="number" value="${skill.order || 1}" onchange="updateSkillOrder('${sectionId}', '${skillId}', this.value)" style="padding: 0.5rem;">
                            </div>
                            <div class="form-group">
                                <label>Skill ID</label>
                                <input type="text" value="${skillId}" onchange="updateSkillId('${sectionId}', '${skillId}', this.value)" style="padding: 0.5rem;">
                            </div>
                        </div>

                        <div class="form-group">
                            <label>Name (English)</label>
                            <input type="text" value="${skill.name.en || ''}" onchange="updateSkillName('${sectionId}', '${skillId}', 'en', this.value)" style="padding: 0.5rem;">
                        </div>
                        <div class="form-group">
                            <label>Name (中文)</label>
                            <input type="text" value="${skill.name.zh || ''}" onchange="updateSkillName('${sectionId}', '${skillId}', 'zh', this.value)" style="padding: 0.5rem;">
                        </div>
                        <div class="form-group">
                            <label>Name (Nederlands)</label>
                            <input type="text" value="${skill.name.nl || ''}" onchange="updateSkillName('${sectionId}', '${skillId}', 'nl', this.value)" style="padding: 0.5rem;">
                        </div>
                        <div class="form-group">
                            <label>Description (English)</label>
                            <textarea onchange="updateSkillDescription('${sectionId}', '${skillId}', 'en', this.value)" style="padding: 0.5rem; min-height: 60px;">${skill.description.en || ''}</textarea>
                        </div>
                    </div>
                `;
            });

            return html;
        }

        // Update functions for template editing
        function updateSectionOrder(sectionId, value) {
            if (templates[selectedTemplate] && templates[selectedTemplate].sections[sectionId]) {
                templates[selectedTemplate].sections[sectionId].order = parseInt(value);
            }
        }

        function updateSectionTitle(sectionId, lang, value) {
            if (templates[selectedTemplate] && templates[selectedTemplate].sections[sectionId]) {
                templates[selectedTemplate].sections[sectionId].title[lang] = value;
            }
        }

        function updateSkillOrder(sectionId, skillId, value) {
            if (templates[selectedTemplate] && templates[selectedTemplate].sections[sectionId] &&
                templates[selectedTemplate].sections[sectionId].skills[skillId]) {
                templates[selectedTemplate].sections[sectionId].skills[skillId].order = parseInt(value);
            }
        }

        function updateSkillName(sectionId, skillId, lang, value) {
            if (templates[selectedTemplate] && templates[selectedTemplate].sections[sectionId] &&
                templates[selectedTemplate].sections[sectionId].skills[skillId]) {
                templates[selectedTemplate].sections[sectionId].skills[skillId].name[lang] = value;
            }
        }

        function updateSkillDescription(sectionId, skillId, lang, value) {
            if (templates[selectedTemplate] && templates[selectedTemplate].sections[sectionId] &&
                templates[selectedTemplate].sections[sectionId].skills[skillId]) {
                templates[selectedTemplate].sections[sectionId].skills[skillId].description[lang] = value;
            }
        }

        // Add new template
        function addNewTemplate() {
            const templateId = `template_${Date.now()}`;
            const newTemplate = {
                id: templateId,
                name: {
                    en: 'New Template',
                    zh: '新模板',
                    nl: 'Nieuwe Template'
                },
                description: {
                    en: 'New feedback template',
                    zh: '新的反馈模板',
                    nl: 'Nieuwe feedback template'
                },
                targetLevel: 'beginner',
                sport: 'snowboard',
                sections: {},
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                createdBy: 'admin',
                version: '1.0'
            };

            templates[templateId] = newTemplate;
            selectedTemplate = templateId;
            renderTemplateList();
            renderTemplateForm(templateId);
            document.getElementById('delete-btn').style.display = 'inline-block';
        }

        // Add new section
        function addNewSection() {
            if (!selectedTemplate) return;

            const sectionId = `section_${Date.now()}`;
            const newSection = {
                title: {
                    en: 'New Section',
                    zh: '新分类',
                    nl: 'Nieuwe Sectie'
                },
                order: Object.keys(templates[selectedTemplate].sections || {}).length + 1,
                skills: {}
            };

            if (!templates[selectedTemplate].sections) {
                templates[selectedTemplate].sections = {};
            }

            templates[selectedTemplate].sections[sectionId] = newSection;
            renderTemplateForm(selectedTemplate);
        }

        // Add new skill
        function addNewSkill(sectionId) {
            if (!selectedTemplate) return;

            const skillId = `skill_${Date.now()}`;
            const newSkill = {
                name: {
                    en: 'New Skill',
                    zh: '新技能',
                    nl: 'Nieuwe Vaardigheid'
                },
                description: {
                    en: 'New skill description',
                    zh: '新技能描述',
                    nl: 'Nieuwe vaardigheid beschrijving'
                },
                order: Object.keys(templates[selectedTemplate].sections[sectionId].skills || {}).length + 1
            };

            if (!templates[selectedTemplate].sections[sectionId].skills) {
                templates[selectedTemplate].sections[sectionId].skills = {};
            }

            templates[selectedTemplate].sections[sectionId].skills[skillId] = newSkill;
            renderTemplateForm(selectedTemplate);
        }

        // Remove section
        function removeSection(sectionId) {
            if (!selectedTemplate || !confirm('Are you sure you want to remove this section?')) return;

            delete templates[selectedTemplate].sections[sectionId];
            renderTemplateForm(selectedTemplate);
        }

        // Remove skill
        function removeSkill(sectionId, skillId) {
            if (!selectedTemplate || !confirm('Are you sure you want to remove this skill?')) return;

            delete templates[selectedTemplate].sections[sectionId].skills[skillId];
            renderTemplateForm(selectedTemplate);
        }

        // Save templates
        async function saveTemplates() {
            if (!selectedTemplate) {
                alert('Please select a template first.');
                return;
            }

            // Update template with form data
            updateTemplateFromForm();

            try {
                const response = await fetch('data/feedback_templates.json', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(templates)
                });

                if (!response.ok) {
                    throw new Error('Failed to save templates');
                }

                showSuccess('Templates saved successfully!');
                renderTemplateList();
            } catch (error) {
                console.error('Error saving templates:', error);
                showError('Failed to save templates. Please try again.');
            }
        }

        // Update template from form
        function updateTemplateFromForm() {
            if (!selectedTemplate) return;

            const template = templates[selectedTemplate];

            // Update basic info
            template.sport = document.getElementById('template-sport').value;
            template.targetLevel = document.getElementById('template-level').value;
            template.version = document.getElementById('template-version').value;

            // Update names
            template.name.en = document.getElementById('name-en').value;
            template.name.zh = document.getElementById('name-zh').value;
            template.name.nl = document.getElementById('name-nl').value;

            // Update descriptions
            template.description.en = document.getElementById('desc-en').value;
            template.description.zh = document.getElementById('desc-zh').value;
            template.description.nl = document.getElementById('desc-nl').value;

            // Update timestamp
            template.updatedAt = new Date().toISOString();
        }

        // Delete template
        function deleteTemplate() {
            if (!selectedTemplate || !confirm('Are you sure you want to delete this template? This action cannot be undone.')) {
                return;
            }

            delete templates[selectedTemplate];
            selectedTemplate = null;
            document.getElementById('delete-btn').style.display = 'none';
            renderTemplateList();
            document.getElementById('form-container').innerHTML = '<div class="no-selection">Select a template from the left panel or create a new one</div>';
        }

        // Preview template
        function previewTemplate() {
            if (!selectedTemplate) {
                alert('Please select a template first.');
                return;
            }

            updateTemplateFromForm();
            const template = templates[selectedTemplate];

            // Create preview window
            const previewWindow = window.open('', '_blank', 'width=800,height=600');
            previewWindow.document.write(generatePreviewHTML(template));
        }

        // Generate preview HTML
        function generatePreviewHTML(template) {
            let sectionsHTML = '';

            Object.entries(template.sections || {}).forEach(([sectionId, section]) => {
                let skillsHTML = '';
                Object.entries(section.skills || {}).forEach(([skillId, skill]) => {
                    skillsHTML += `
                        <div style="margin: 0.5rem 0; padding: 0.5rem; background: #f0f0f0; border-radius: 4px;">
                            <strong>${skill.name.en}</strong><br>
                            <small style="color: #666;">${skill.description.en}</small>
                        </div>
                    `;
                });

                sectionsHTML += `
                    <div style="margin: 1rem 0; padding: 1rem; border: 1px solid #ddd; border-radius: 8px;">
                        <h3 style="color: #E53512; margin-bottom: 1rem;">${section.title.en}</h3>
                        ${skillsHTML}
                    </div>
                `;
            });

            return `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Template Preview: ${template.name.en}</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 2rem; }
                        .header { text-align: center; margin-bottom: 2rem; }
                        .badge { padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.8rem; margin: 0 0.2rem; }
                        .badge-beginner { background: #28a745; color: white; }
                        .badge-intermediate { background: #ffc107; color: white; }
                        .badge-advanced { background: #dc3545; color: white; }
                        .badge-snowboard { background: #9ED4E7; color: white; }
                        .badge-ski { background: #E53512; color: white; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>${template.name.en}</h1>
                        <p>${template.description.en}</p>
                        <div>
                            <span class="badge badge-${template.targetLevel}">${template.targetLevel}</span>
                            <span class="badge badge-${template.sport}">${template.sport}</span>
                        </div>
                    </div>
                    ${sectionsHTML}
                </body>
                </html>
            `;
        }

        // Initialize page when DOM is loaded
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
