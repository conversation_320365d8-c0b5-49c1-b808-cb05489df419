<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate Generator Test - SnowNavi</title>
    <style>
        :root {
            --main-red: #E53512;
            --bg-light: #F9F4F3;
            --text-dark: #2F2F2F;
            --text-gray: #717171;
            --contrast-white: #FFFFFF;
            --accent-blue: #9ED4E7;
        }

        body { 
            font-family: 'Noto Sans SC', sans-serif; 
            margin: 2rem; 
            background: var(--bg-light);
            color: var(--text-dark);
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: var(--contrast-white);
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 3px solid var(--main-red);
        }
        h1 { color: var(--main-red); }
        h2 { color: var(--text-dark); border-bottom: 2px solid var(--main-red); padding-bottom: 0.5rem; }
        .test-section { 
            margin: 2rem 0; 
            padding: 1.5rem; 
            border: 1px solid rgba(229, 53, 18, 0.1);
            border-radius: 8px;
            background: var(--bg-light);
        }
        .certificate-preview {
            text-align: center;
            margin: 2rem 0;
        }
        .preview-canvas {
            border: 2px solid var(--main-red);
            border-radius: 8px;
            max-width: 100%;
            height: auto;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        .test-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        .control-group {
            background: var(--contrast-white);
            padding: 1rem;
            border-radius: 6px;
            border: 1px solid rgba(229, 53, 18, 0.1);
        }
        .control-group label {
            display: block;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--main-red);
        }
        .control-group input, .control-group select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        .btn {
            background: var(--main-red);
            color: var(--contrast-white);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 0.5rem;
        }
        .btn:hover {
            background: #c42e0f;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(229, 53, 18, 0.3);
        }
        .btn:disabled {
            background: var(--text-gray);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .btn-secondary {
            background: var(--accent-blue);
            color: var(--text-dark);
        }
        .btn-secondary:hover {
            background: #7bc3d4;
        }
        .feature-list {
            background: var(--contrast-white);
            padding: 1rem;
            border-radius: 6px;
            border-left: 4px solid var(--main-red);
        }
        .feature-list h4 {
            color: var(--main-red);
            margin: 0 0 0.5rem 0;
        }
        .feature-list ul {
            margin: 0;
            padding-left: 1.5rem;
        }
        .feature-list li {
            margin-bottom: 0.25rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📜 Certificate Generator Test</h1>
            <p>Test and preview the SnowNavi course completion certificate</p>
        </div>
        
        <div class="test-section">
            <h2>🎨 Certificate Features</h2>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                <div class="feature-list">
                    <h4>Design Elements</h4>
                    <ul>
                        <li>3:4 aspect ratio (1200x1600px)</li>
                        <li>SnowNavi branding and colors</li>
                        <li>Decorative borders and gradients</li>
                        <li>Professional typography</li>
                    </ul>
                </div>
                
                <div class="feature-list">
                    <h4>Student Information</h4>
                    <ul>
                        <li>Student name prominently displayed</li>
                        <li>Member ID for verification</li>
                        <li>Course name and date</li>
                        <li>Member QR code for verification</li>
                    </ul>
                </div>
                
                <div class="feature-list">
                    <h4>Skill Assessment</h4>
                    <ul>
                        <li>Visual progress bars by section</li>
                        <li>Overall completion percentage</li>
                        <li>Color-coded skill categories</li>
                        <li>Detailed skill breakdown</li>
                    </ul>
                </div>
                
                <div class="feature-list">
                    <h4>Contact & Social</h4>
                    <ul>
                        <li>SnowNavi contact information</li>
                        <li>WeChat QR code</li>
                        <li>Xiaohongshu QR code</li>
                        <li>Issue date and verification</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🧪 Test Certificate Generation</h2>
            
            <div class="test-controls">
                <div class="control-group">
                    <label for="test-name">Student Name:</label>
                    <input type="text" id="test-name" value="Zhang Wei" placeholder="Enter student name">
                </div>
                
                <div class="control-group">
                    <label for="test-id">Member ID:</label>
                    <input type="text" id="test-id" value="SN20250001" placeholder="Enter member ID">
                </div>
                
                <div class="control-group">
                    <label for="test-course">Course Name:</label>
                    <input type="text" id="test-course" value="Beginner Snowboard Lesson" placeholder="Enter course name">
                </div>
                
                <div class="control-group">
                    <label for="test-date">Course Date:</label>
                    <input type="date" id="test-date" value="2025-01-27">
                </div>
                
                <div class="control-group">
                    <label for="test-skills">Completed Skills:</label>
                    <select id="test-skills">
                        <option value="beginner">Beginner (5 skills)</option>
                        <option value="intermediate">Intermediate (12 skills)</option>
                        <option value="advanced" selected>Advanced (20 skills)</option>
                        <option value="expert">Expert (25 skills)</option>
                    </select>
                </div>
            </div>
            
            <div style="text-align: center; margin: 2rem 0;">
                <button class="btn" onclick="generateTestCertificate()">🎨 Generate Preview</button>
                <button class="btn btn-secondary" onclick="downloadTestCertificate()">📥 Download Certificate</button>
            </div>
        </div>
        
        <div class="certificate-preview">
            <h2>📋 Certificate Preview</h2>
            <canvas id="preview-canvas" class="preview-canvas" style="display: none;"></canvas>
            <div id="preview-placeholder" style="padding: 3rem; color: var(--text-gray); border: 2px dashed #ddd; border-radius: 8px;">
                Click "Generate Preview" to see the certificate
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔗 Integration Test</h2>
            <p>Test the certificate feature in the actual student feedback portal:</p>
            
            <div style="text-align: center;">
                <a href="student_feedback.html" target="_blank" class="btn">🎿 Open Student Portal</a>
                <a href="test_student_feedback.html" target="_blank" class="btn btn-secondary">📚 View Test Guide</a>
            </div>
            
            <div style="margin-top: 1rem; padding: 1rem; background: var(--bg-light); border-radius: 6px;">
                <h4 style="color: var(--main-red); margin: 0 0 0.5rem 0;">Test Instructions:</h4>
                <ol>
                    <li>Open the student portal and search for a test email (e.g., <EMAIL>)</li>
                    <li>Select a course with feedback</li>
                    <li>Scroll down to the "Course Completion Certificate" section</li>
                    <li>Click "Download Certificate" to test the full integration</li>
                </ol>
            </div>
        </div>
    </div>

    <script src="certificate-generator.js"></script>
    <script>
        let certificateGenerator = new CertificateGenerator();
        
        function getTestSkillData(level) {
            const skillSets = {
                beginner: ['equipment-intro', 'single-foot-familiarity', 'single-foot-sliding', 'single-foot-climbing', 'single-foot-straight'],
                intermediate: ['equipment-intro', 'single-foot-familiarity', 'single-foot-sliding', 'single-foot-climbing', 'single-foot-straight', 'single-foot-heel-brake', 'single-foot-j-turn', 'static-gas-pedal', 'single-heel-side-push', 'single-toe-side-push', 'both-heel-side-push', 'both-toe-side-push'],
                advanced: ['equipment-intro', 'single-foot-familiarity', 'single-foot-sliding', 'single-foot-climbing', 'single-foot-straight', 'single-foot-heel-brake', 'single-foot-j-turn', 'static-gas-pedal', 'single-heel-side-push', 'single-toe-side-push', 'both-heel-side-push', 'both-toe-side-push', 'both-heel-falling-leaf', 'both-toe-falling-leaf', 'both-heel-power-falling-leaf', 'both-toe-power-falling-leaf', 'static-rotation', 'step-turns', 'j-turns', 'walking-edge-change'],
                expert: ['equipment-intro', 'single-foot-familiarity', 'single-foot-sliding', 'single-foot-climbing', 'single-foot-straight', 'single-foot-heel-brake', 'single-foot-j-turn', 'static-gas-pedal', 'single-heel-side-push', 'single-toe-side-push', 'both-heel-side-push', 'both-toe-side-push', 'both-heel-falling-leaf', 'both-toe-falling-leaf', 'both-heel-power-falling-leaf', 'both-toe-power-falling-leaf', 'static-rotation', 'step-turns', 'j-turns', 'walking-edge-change', 'beginner-turns', 'edge-change-traverse', 'traverse-body-movement', 'continuous-edge-change', 'scrub-360']
            };
            
            return {
                completedSkills: skillSets[level] || skillSets.beginner,
                sectionFeedbacks: {
                    basic: "Excellent foundation skills",
                    sliding: "Great progress on movement",
                    control: "Good edge control development",
                    turning: "Turning technique improving",
                    flow: "Advanced flow techniques mastered"
                }
            };
        }
        
        async function generateTestCertificate() {
            const name = document.getElementById('test-name').value;
            const memberId = document.getElementById('test-id').value;
            const course = document.getElementById('test-course').value;
            const date = document.getElementById('test-date').value;
            const skillLevel = document.getElementById('test-skills').value;
            
            const certificateData = {
                memberName: name,
                memberId: memberId,
                activityName: course,
                activityDate: new Date(date).toLocaleDateString(),
                feedback: {
                    skillAssessment: getTestSkillData(skillLevel)
                }
            };
            
            try {
                const canvas = await certificateGenerator.generateAdvancedCertificate(certificateData);
                
                // Show preview
                const previewCanvas = document.getElementById('preview-canvas');
                const placeholder = document.getElementById('preview-placeholder');
                
                // Copy canvas content to preview canvas
                previewCanvas.width = canvas.width;
                previewCanvas.height = canvas.height;
                const previewCtx = previewCanvas.getContext('2d');
                previewCtx.drawImage(canvas, 0, 0);
                
                // Show preview, hide placeholder
                previewCanvas.style.display = 'block';
                placeholder.style.display = 'none';
                
            } catch (error) {
                console.error('Error generating certificate:', error);
                alert('Failed to generate certificate preview');
            }
        }
        
        async function downloadTestCertificate() {
            const name = document.getElementById('test-name').value;
            const memberId = document.getElementById('test-id').value;
            const course = document.getElementById('test-course').value;
            const date = document.getElementById('test-date').value;
            const skillLevel = document.getElementById('test-skills').value;
            
            const certificateData = {
                memberName: name,
                memberId: memberId,
                activityName: course,
                activityDate: new Date(date).toLocaleDateString(),
                feedback: {
                    skillAssessment: getTestSkillData(skillLevel)
                }
            };
            
            try {
                const canvas = await certificateGenerator.generateAdvancedCertificate(certificateData);
                
                canvas.toBlob((blob) => {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `SnowNavi_Certificate_${name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.jpg`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }, 'image/jpeg', 0.95);
                
            } catch (error) {
                console.error('Error downloading certificate:', error);
                alert('Failed to download certificate');
            }
        }
        
        // Generate initial preview
        window.onload = () => {
            setTimeout(generateTestCertificate, 500);
        };
    </script>
</body>
</html>
