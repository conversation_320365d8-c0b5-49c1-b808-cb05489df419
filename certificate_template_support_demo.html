<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate Template Support Demo - SnowNavi</title>
    <style>
        :root {
            --main-red: #E53512;
            --bg-light: #F9F4F3;
            --text-dark: #2F2F2F;
            --text-gray: #717171;
            --contrast-white: #FFFFFF;
            --accent-blue: #9ED4E7;
            --success-green: #28a745;
            --warning-orange: #ffc107;
        }

        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 2rem; 
            background: var(--bg-light);
            color: var(--text-dark);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--contrast-white);
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 3px solid var(--main-red);
        }
        h1 { color: var(--main-red); }
        .demo-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid var(--main-red);
            background: var(--bg-light);
        }
        .demo-section h3 {
            color: var(--main-red);
            margin: 0 0 1rem 0;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        .feature-card {
            background: var(--contrast-white);
            border-radius: 8px;
            padding: 1.5rem;
            border: 2px solid var(--accent-blue);
            transition: transform 0.2s ease;
        }
        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }
        .feature-card h4 {
            color: var(--main-red);
            margin-bottom: 1rem;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 1rem 0;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 0.75rem;
            text-align: left;
        }
        .comparison-table th {
            background: var(--main-red);
            color: white;
        }
        .comparison-table tr:nth-child(even) {
            background: var(--bg-light);
        }
        .status-badge {
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .badge-implemented {
            background: var(--success-green);
            color: white;
        }
        .badge-enhanced {
            background: var(--warning-orange);
            color: white;
        }
        .workflow-step {
            display: flex;
            align-items: flex-start;
            margin: 1rem 0;
            padding: 1rem;
            background: var(--contrast-white);
            border-radius: 8px;
            border-left: 4px solid var(--success-green);
        }
        .step-number {
            background: var(--main-red);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
            flex-shrink: 0;
        }
        .btn {
            background: var(--main-red);
            color: var(--contrast-white);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 0.5rem;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #c42e0f;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(229, 53, 18, 0.3);
        }
        .btn-secondary {
            background: var(--text-gray);
        }
        .btn-secondary:hover {
            background: #5a5a5a;
        }
        .highlight {
            background: rgba(229, 53, 18, 0.1);
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            font-weight: bold;
            color: var(--main-red);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📜 Certificate Template Support</h1>
            <p>Dynamic certificate generation based on feedback templates</p>
        </div>
        
        <div class="demo-section">
            <h3>🎯 Enhanced Certificate System</h3>
            <p>The certificate generation system now supports dynamic content based on feedback templates. This means certificates automatically adapt to show the correct skill sections, progress bars, and completion statistics based on the specific course template used.</p>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🔄 Dynamic Skill Sections</h4>
                    <p>Certificate progress bars are generated from template sections</p>
                    <ul>
                        <li>Automatic section detection from templates</li>
                        <li>Custom colors for each section</li>
                        <li>Multi-language section names</li>
                        <li>Accurate skill counts per section</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4>📊 Accurate Progress Calculation</h4>
                    <p>Total skills and completion rates calculated from template data</p>
                    <ul>
                        <li>Dynamic total skill counting</li>
                        <li>Template-based progress calculation</li>
                        <li>Fallback to default structure</li>
                        <li>Real-time completion percentages</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4>🌐 Template Integration</h4>
                    <p>Seamless integration with feedback template system</p>
                    <ul>
                        <li>Automatic template loading</li>
                        <li>Activity-template association</li>
                        <li>Error handling and fallbacks</li>
                        <li>Template validation</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>🔧 Technical Implementation</h3>
            
            <div class="workflow-step">
                <div class="step-number">1</div>
                <div>
                    <strong>Template Data Loading</strong><br>
                    When generating a certificate, the system loads the feedback template associated with the activity.
                    <div class="code-block">
// Get template data for the activity
let templateData = null;
if (feedback && feedback.activityId && activities[feedback.activityId]) {
    const activity = activities[feedback.activityId];
    const templateId = activity.feedbackTemplateId;
    
    if (templateId && feedbackTemplates[templateId]) {
        templateData = feedbackTemplates[templateId];
        console.log(`Using template: ${templateData.name?.en} for certificate`);
    }
}
                    </div>
                </div>
            </div>
            
            <div class="workflow-step">
                <div class="step-number">2</div>
                <div>
                    <strong>Dynamic Section Generation</strong><br>
                    Progress bars are generated based on template sections rather than hardcoded structure.
                    <div class="code-block">
if (templateData && templateData.sections) {
    // Build sections from template data
    const sortedSections = Object.entries(templateData.sections).sort((a, b) => {
        return (a[1].order || 0) - (b[1].order || 0);
    });
    
    sections = sortedSections.map(([sectionId, section]) => {
        const skillCount = Object.keys(section.skills || {}).length;
        return {
            id: sectionId,
            name: section.title[language] || section.title.en,
            total: skillCount,
            skills: Object.keys(section.skills || {})
        };
    });
}
                    </div>
                </div>
            </div>
            
            <div class="workflow-step">
                <div class="step-number">3</div>
                <div>
                    <strong>Progress Calculation</strong><br>
                    Total skills and completion rates are calculated dynamically from template data.
                    <div class="code-block">
// Calculate total skills dynamically from template
let totalSkills = 25; // Default fallback
if (templateData && templateData.sections) {
    totalSkills = Object.values(templateData.sections).reduce((total, section) => {
        return total + Object.keys(section.skills || {}).length;
    }, 0);
}

const completionRate = totalSkills > 0 ? 
    Math.round((completedSkills.length / totalSkills) * 100) : 0;
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>📋 Feature Comparison</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Feature</th>
                        <th>Before (Static)</th>
                        <th>After (Template-Based)</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Skill Sections</td>
                        <td>5 hardcoded sections</td>
                        <td>Dynamic sections from template</td>
                        <td><span class="status-badge badge-enhanced">✅ Enhanced</span></td>
                    </tr>
                    <tr>
                        <td>Section Names</td>
                        <td>Fixed English names</td>
                        <td>Multi-language from template</td>
                        <td><span class="status-badge badge-enhanced">✅ Enhanced</span></td>
                    </tr>
                    <tr>
                        <td>Skill Counting</td>
                        <td>Fixed 25 total skills</td>
                        <td>Dynamic count from template</td>
                        <td><span class="status-badge badge-enhanced">✅ Enhanced</span></td>
                    </tr>
                    <tr>
                        <td>Progress Calculation</td>
                        <td>Based on hardcoded structure</td>
                        <td>Based on actual template skills</td>
                        <td><span class="status-badge badge-enhanced">✅ Enhanced</span></td>
                    </tr>
                    <tr>
                        <td>Template Integration</td>
                        <td>No template support</td>
                        <td>Full template integration</td>
                        <td><span class="status-badge badge-implemented">✅ New</span></td>
                    </tr>
                    <tr>
                        <td>Fallback Support</td>
                        <td>N/A</td>
                        <td>Graceful fallback to defaults</td>
                        <td><span class="status-badge badge-implemented">✅ New</span></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="demo-section">
            <h3>🎨 Certificate Enhancements</h3>
            <p>The enhanced certificate system provides several key improvements:</p>
            
            <div class="code-block">
// Enhanced certificate generation with template support
async generateAdvancedCertificate(data, language = 'en', templateData = null) {
    // ... existing code ...
    
    // Skills progress section with template support
    if (data.feedback && data.feedback.skillAssessment) {
        this.drawSkillProgress(ctx, 150, 760, width - 300, 30, 
                             data.feedback.skillAssessment, language, templateData);
        
        // Dynamic total skills calculation
        let totalSkills = 25; // Default fallback
        if (templateData && templateData.sections) {
            totalSkills = Object.values(templateData.sections).reduce((total, section) => {
                return total + Object.keys(section.skills || {}).length;
            }, 0);
        }
        
        const completionRate = totalSkills > 0 ? 
            Math.round((completedSkills.length / totalSkills) * 100) : 0;
    }
}
            </div>
        </div>
        
        <div style="text-align: center; margin: 2rem 0;">
            <h3 style="color: var(--main-red); margin-bottom: 1rem;">🧪 Test Certificate Generation</h3>
            <a href="student_feedback.html" target="_blank" class="btn">📜 Student Feedback & Certificates</a>
            <a href="certificate_preview.html" target="_blank" class="btn">🔍 Certificate Preview</a>
            <a href="feedback_template_admin.html" target="_blank" class="btn btn-secondary">📝 Template Management</a>
        </div>
        
        <div style="background: var(--bg-light); padding: 1.5rem; border-radius: 8px; text-align: center;">
            <h4 style="color: var(--main-red); margin: 0 0 1rem 0;">📜 Template-Based Certificates Complete</h4>
            <p>Certificates now dynamically adapt to feedback templates, providing accurate skill assessment visualization and progress tracking based on the specific course structure and requirements.</p>
        </div>
    </div>
</body>
</html>
