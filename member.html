<!DOCTYPE html>
<html lang="en">
<head>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-T4N8L0SRWZ"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-T4N8L0SRWZ');
  </script>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title id="page-title">SnowNavi Member Information</title>

  <!-- Favicon -->
  <link rel="icon" type="image/png" sizes="32x32" href="assets/picture/snownavi_logo.png">
  <link rel="icon" type="image/png" sizes="16x16" href="assets/picture/snownavi_logo.png">
  <link rel="shortcut icon" href="assets/picture/snownavi_logo.png">

  <style>
    :root {
      --main-red: #E53512;
      --bg-light: #F9F4F3;
      --text-dark: #2F2F2F;
      --text-gray: #717171;
      --contrast-white: #FFFFFF;
      --accent-blue: #9ED4E7;
    }

    body {
      margin: 0;
      font-family: 'Noto Sans SC', sans-serif;
      background-color: var(--bg-light);
      color: var(--text-dark);
    }

    header {
      background: var(--contrast-white);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem 2rem;
      box-shadow: 0 2px 6px rgba(0,0,0,0.05);
      position: relative;
    }

    header img {
      height: 40px;
    }

    nav {
      display: flex;
      align-items: center;
    }

    nav a {
      margin-left: 1.5rem;
      text-decoration: none;
      color: var(--text-dark);
      font-weight: bold;
    }

    .language-selector {
      margin-left: 2rem;
      font-size: 1rem;
    }

    .menu-toggle {
      display: none;
      font-size: 1.5rem;
      background: none;
      border: none;
      cursor: pointer;
      color: var(--text-dark);
    }

    .nav-links {
      display: flex;
      align-items: center;
    }

    @media (max-width: 768px) {
      .menu-toggle {
        display: block;
      }

      .nav-links {
        display: none;
        flex-direction: column;
        background: var(--contrast-white);
        position: absolute;
        top: 100%;
        right: 0;
        width: 200px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        z-index: 10;
      }

      .nav-links.open {
        display: flex;
      }

      .nav-links a, .nav-links .language-selector {
        margin: 1rem;
      }
    }

    .section {
      padding: 3rem 2rem;
      max-width: 800px;
      margin: auto;
    }

    .member-card {
      background: var(--contrast-white);
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      padding: 2rem;
      margin-top: 2rem;
    }

    .member-header {
      display: flex;
      align-items: center;
      margin-bottom: 2rem;
      border-bottom: 1px solid #eee;
      padding-bottom: 1rem;
    }

    .member-avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background-color: var(--accent-blue);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2rem;
      color: var(--contrast-white);
      margin-right: 1.5rem;
    }

    .member-name {
      font-size: 1.8rem;
      margin: 0;
      color: var(--text-dark);
    }

    .member-id {
      font-size: 1rem;
      color: var(--text-gray);
      margin: 0.5rem 0 0 0;
    }

    .member-info {
      margin-bottom: 1.5rem;
    }

    .info-row {
      display: flex;
      margin-bottom: 1rem;
    }

    .info-label {
      width: 40%;
      font-weight: bold;
      color: var(--text-dark);
    }

    .info-value {
      width: 60%;
      color: var(--text-gray);
    }

    .membership-status {
      display: inline-block;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-weight: bold;
      margin-top: 0.5rem;
    }

    .status-active {
      background-color: #e6f7e6;
      color: #2e7d32;
    }

    .status-expired {
      background-color: #ffebee;
      color: #c62828;
    }

    .qr-section {
      margin-top: 2rem;
      border-top: 1px solid #eee;
      padding-top: 1.5rem;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .qr-title {
      font-size: 1.2rem;
      font-weight: bold;
      margin-bottom: 1rem;
      color: var(--text-dark);
    }

    .qr-code {
      width: 200px;
      height: 200px;
      background-color: #fff;
      padding: 1rem;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .qr-code img {
      width: 100%;
      height: 100%;
    }

    .qr-description {
      margin-top: 1rem;
      text-align: center;
      color: var(--text-gray);
      font-size: 0.9rem;
      max-width: 300px;
    }

    @media (max-width: 768px) {
      .qr-code {
        width: 150px;
        height: 150px;
      }
    }

    footer {
      background: var(--text-dark);
      color: white;
      padding: 2rem;
      text-align: center;
      margin-top: 3rem;
    }
  </style>
</head>
<body>
  <header>
    <img src="assets/picture/snownavi_logo_banner.jpg" alt="SnowNavi logo">
    <button class="menu-toggle" id="menu-toggle">☰</button>
    <nav class="nav-links" id="nav-links">
      <!-- Navigation items will be dynamically inserted here -->
      <div class="language-selector">
        <select id="lang" class="language-selector">
          <option value="en">🇬🇧 EN</option>
          <option value="zh">🇨🇳 中文</option>
          <option value="nl">🇳🇱 NL</option>
        </select>
      </div>
    </nav>
  </header>

  <div class="section">
    <h1 id="member-page-title">Member Information</h1>

    <div class="member-card">
      <div class="member-header">
        <div class="member-avatar" id="member-avatar">JS</div>
        <div>
          <h2 class="member-name" id="member-name">John Snow</h2>
          <p class="member-id" id="member-id">ID: SN20250001</p>
        </div>
      </div>

      <div class="member-info">
        <div class="info-row">
          <div class="info-label" id="membership-type-label">Membership Type:</div>
          <div class="info-value">
            <span class="membership-status status-active" id="membership-status">Full Membership</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-label" id="validity-period-label">Validity Period:</div>
          <div class="info-value" id="validity-period">January 1, 2025 - December 31, 2025</div>
        </div>
      </div>

      <div class="qr-section">
        <div class="qr-title" id="qr-title">Share Membership Card</div>
        <div class="qr-code" id="qr-code">
          <!-- QR code will be inserted here -->
        </div>
        <div class="qr-description" id="qr-description">
          Scan this QR code to share your membership information
        </div>
      </div>
    </div>
  </div>

  <footer>
    <p id="contact-text">Contact: <EMAIL> | Follow our 微信公共号 SnowNavi指雪针 for updates</p>
    <p id="copyright">&copy; 2025 SnowNavi Sports. All rights reserved.</p>
  </footer>

  <!-- Include the navigation module -->
  <script src="assets/js/navigation.js"></script>

  <script>
    // Toggle mobile menu
    document.getElementById('menu-toggle').addEventListener('click', function() {
      document.getElementById('nav-links').classList.toggle('open');
    });

    // Language handling
    const savedLang = localStorage.getItem('preferredLang') ||
                     (navigator.language.startsWith('zh') ? 'zh' :
                      navigator.language.startsWith('nl') ? 'nl' : 'en');
    document.getElementById('lang').value = savedLang;

    // Ensure navigation bar language is synchronized immediately
    if (window.navigationManager) {
      window.navigationManager.currentLang = savedLang;
      window.navigationManager.renderNavigation();
    }

    // Translations
    const translations = {
      en: {
        pageTitle: 'SnowNavi Member Information',
        navCourses: 'Courses',
        navMap: 'Interactive Ski Map',
        navStory: 'Our Story',
        navMember: 'Member',
        navContact: 'Contact',
        memberPageTitle: 'Member Information',
        membershipTypeLabel: 'Membership Type:',
        validityPeriodLabel: 'Validity Period:',
        fullMembership: 'Full Membership',
        membershipExpired: 'Membership Expired',
        memberNotFound: 'Member Not Found',
        memberNotFoundMessage: 'The requested member information could not be found. Please check the member ID or contact support.',
        qrTitle: 'Share Membership Card',
        qrDescription: 'Scan this QR code to share your membership information',
        contactText: 'Contact: <EMAIL> | Follow our 微信公共号 SnowNavi指雪针 for updates',
        copyright: '© 2025 SnowNavi Sports. All rights reserved.'
      },
      zh: {
        pageTitle: 'SnowNavi 会员信息',
        navCourses: '课程',
        navMap: '在线滑雪地图',
        navStory: '我们的故事',
        navMember: '会员',
        navContact: '联系我们',
        memberPageTitle: '会员信息',
        membershipTypeLabel: '会员类型:',
        validityPeriodLabel: '有效期:',
        fullMembership: '正式会员',
        membershipExpired: '会员已过期',
        memberNotFound: '未找到会员',
        memberNotFoundMessage: '无法找到请求的会员信息。请检查会员ID或联系客服。',
        qrTitle: '分享会员卡',
        qrDescription: '扫描此二维码分享您的会员信息',
        contactText: '联系方式：<EMAIL> | 请关注微信公共号： SnowNavi指雪针',
        copyright: '© 2025 SnowNavi Sports. 保留所有权利.'
      },
      nl: {
        pageTitle: 'SnowNavi Ledeninformatie',
        navCourses: 'Cursussen',
        navMap: 'Interactieve Skikaart',
        navStory: 'Ons Verhaal',
        navMember: 'Lid',
        navContact: 'Contact',
        memberPageTitle: 'Ledeninformatie',
        membershipTypeLabel: 'Lidmaatschapstype:',
        validityPeriodLabel: 'Geldigheidsperiode:',
        fullMembership: 'Volledig Lidmaatschap',
        membershipExpired: 'Lidmaatschap Verlopen',
        memberNotFound: 'Lid Niet Gevonden',
        memberNotFoundMessage: 'De gevraagde ledeninformatie kon niet worden gevonden. Controleer het lidmaatschapsnummer of neem contact op met de klantenservice.',
        qrTitle: 'Deel Lidmaatschapskaart',
        qrDescription: 'Scan deze QR-code om uw lidmaatschapsinformatie te delen',
        contactText: 'Contact: <EMAIL> | Volg ons op 微信公共号: SnowNavi指雪针',
        copyright: '© 2025 SnowNavi Sports. Alle rechten voorbehouden.'
      }
    };

    // Apply translations
    function applyTranslations(lang) {
      const t = translations[lang];

      // Page title
      document.getElementById('page-title').textContent = t.pageTitle;

      // Member page content
      document.getElementById('member-page-title').textContent = t.memberPageTitle;
      document.getElementById('membership-type-label').textContent = t.membershipTypeLabel;
      document.getElementById('validity-period-label').textContent = t.validityPeriodLabel;

      // Update membership status text based on current status
      const statusElement = document.getElementById('membership-status');
      if (statusElement.classList.contains('status-active')) {
        statusElement.textContent = t.fullMembership;
      } else {
        statusElement.textContent = t.membershipExpired;
      }

      // QR code section
      document.getElementById('qr-title').textContent = t.qrTitle;
      document.getElementById('qr-description').textContent = t.qrDescription;

      // Footer
      document.getElementById('contact-text').textContent = t.contactText;
      document.getElementById('copyright').textContent = t.copyright;

      // Trigger navigation update with the new language
      if (window.navigationManager) {
        window.navigationManager.currentLang = lang;
        window.navigationManager.renderNavigation();
      }
    }

    // Apply initial translations
    applyTranslations(savedLang);

    // Handle language change
    document.getElementById('lang').addEventListener('change', function() {
      const selectedLang = this.value;
      localStorage.setItem('preferredLang', selectedLang);
      applyTranslations(selectedLang);
    });

    // Function to parse date from dd/mm/yyyy format
    function parseDateDDMMYYYY(dateStr) {
      const [day, month, year] = dateStr.split('/').map(part => parseInt(part, 10));
      return new Date(year, month - 1, day);
    }

    // Function to check if validity period has expired
    function checkValidityPeriodExpired(validityPeriodStr) {
      try {
        // Check if the validity period is in the dd/mm/yyyy - dd/mm/yyyy format
        const match = validityPeriodStr.match(/^(\d{2}\/\d{2}\/\d{4}) - (\d{2}\/\d{2}\/\d{4})$/);
        if (match) {
          const endDateStr = match[2];
          const endDate = parseDateDDMMYYYY(endDateStr);

          // Set time to end of day (23:59:59)
          endDate.setHours(23, 59, 59, 999);

          // Compare with current date
          const today = new Date();
          return today > endDate;
        }

        // If the format doesn't match, try to extract the end date from other formats
        // This is a fallback for any validity periods not yet converted to dd/mm/yyyy format
        const parts = validityPeriodStr.split(' - ');
        if (parts.length === 2) {
          // Try to extract a year from the end date part
          const yearMatch = parts[1].match(/\d{4}/);
          if (yearMatch) {
            const year = parseInt(yearMatch[0], 10);
            // If the year is in the past, consider it expired
            return new Date().getFullYear() > year;
          }
        }

        // If we can't determine, default to not expired
        return false;
      } catch (e) {
        console.error('Error checking validity period:', e);
        return false;
      }
    }

    // Function to notify the server to check and update member status if expired
    async function notifyServerToCheckExpiration(memberId) {
      try {
        // Instead of directly updating the member data, call a dedicated API endpoint
        // that will handle the expiration check on the server side
        const response = await fetch(`/api/member/${memberId}/check-expiration`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            // Add a CSRF token if your server implements CSRF protection
            // 'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
          },
          // Send minimal data - just enough for the server to identify the request
          body: JSON.stringify({
            requestTime: new Date().toISOString()
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(`Server error: ${errorData.error || 'Unknown error'}`);
        }

        const result = await response.json();
        console.log('Expiration check result:', result.message);
        return result.updated; // Return whether the status was updated

      } catch (error) {
        console.error('Error checking member expiration:', error);
        return false;
      }
    }

    // Function to set member data
    async function setMemberData(data) {
      // Set member name and update avatar initials
      document.getElementById('member-name').textContent = data.name;
      const initials = data.name.split(' ').map(n => n[0]).join('');
      document.getElementById('member-avatar').textContent = initials;

      // Set member ID
      document.getElementById('member-id').textContent = `ID: ${data.id}`;

      // Get validity period (now a single string instead of an object with language keys)
      let validityPeriod = '';

      if (typeof data.validityPeriod === 'string') {
        // New format: single string
        validityPeriod = data.validityPeriod;
      } else if (typeof data.validityPeriod === 'object') {
        // Old format: object with language keys
        validityPeriod = data.validityPeriod[savedLang] || data.validityPeriod.en || '';
      }

      // Check if validity period has expired
      const isExpired = checkValidityPeriodExpired(validityPeriod);

      // Local variable to track if we should display as expired
      let displayAsExpired = !data.isActive || isExpired;

      // If the member appears active but the validity period has expired,
      // notify the server to perform the expiration check securely
      if (isExpired && data.isActive) {
        // Notify the server to check and update expiration status
        // This is a more secure approach as the server will validate the request
        const wasUpdated = await notifyServerToCheckExpiration(data.id);

        // If the server confirmed an update, reflect that in our display
        if (wasUpdated) {
          displayAsExpired = true;
        }
      }

      // Set membership status
      const statusElement = document.getElementById('membership-status');
      if (!displayAsExpired) {
        statusElement.classList.remove('status-expired');
        statusElement.classList.add('status-active');
        statusElement.textContent = translations[savedLang].fullMembership;
      } else {
        statusElement.classList.remove('status-active');
        statusElement.classList.add('status-expired');
        statusElement.textContent = translations[savedLang].membershipExpired;
      }

      // Set validity period text
      document.getElementById('validity-period').textContent = validityPeriod;
    }

    // Function to fetch member data from API
    async function fetchMemberData() {
      try {
        // Get member ID from URL parameter
        const urlParams = new URLSearchParams(window.location.search);
        const memberId = urlParams.get('id');

        // If no member ID is provided, show an error message
        if (!memberId) {
          throw new Error('No member ID provided');
        }

        // Validate member ID format to prevent injection attacks
        if (!validateMemberId(memberId)) {
          throw new Error('Invalid member ID format');
        }

        // Fetch member data from API
        const response = await fetch(`/api/member/${memberId}`, {
          headers: {
            // Add cache control to prevent caching of sensitive data
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to fetch member data');
        }

        const data = await response.json();

        // Validate the received data structure before processing
        if (!validateMemberData(data)) {
          throw new Error('Invalid member data received');
        }

        // Now set the member data (this is now an async function)
        await setMemberData(data);

        // Generate QR code after member data is loaded
        generateQRCode(window.location.href);
      } catch (error) {
        console.error('Error fetching member data:', error);
        // Show error message to user
        document.querySelector('.member-card').innerHTML = `
          <div style="text-align: center; padding: 2rem;">
            <h3>${translations[savedLang].memberNotFound}</h3>
            <p>${translations[savedLang].memberNotFoundMessage}</p>
          </div>
        `;
      }
    }

    // Function to validate member ID format
    function validateMemberId(id) {
      // Check if the ID matches the pattern: SN + 4-digit year + 4-digit sequence
      const pattern = /^SN\d{4}\d{4}$/;
      return pattern.test(id);
    }

    // Function to validate member data structure
    function validateMemberData(data) {
      // Check if the data has the required properties
      return data &&
             typeof data === 'object' &&
             'id' in data &&
             'name' in data &&
             'isActive' in data &&
             'validityPeriod' in data &&
             (typeof data.validityPeriod === 'object' || typeof data.validityPeriod === 'string');
    }

    // Function to generate QR code
    function generateQRCode(url) {
      // Use the QR Server API to generate a QR code
      const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(url)}`;

      // Create an image element
      const qrImage = document.createElement('img');
      qrImage.src = qrCodeUrl;
      qrImage.alt = 'QR Code';

      // Add the image to the QR code container
      const qrCodeContainer = document.getElementById('qr-code');
      qrCodeContainer.innerHTML = '';
      qrCodeContainer.appendChild(qrImage);
    }

    // Fetch and display member data
    fetchMemberData();
  </script>
</body>
</html>
