<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Tracking Demo - SnowNavi Feedback System</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 2rem; 
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .user-info {
            background: #e3f2fd;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 2rem;
            border-left: 4px solid #2196f3;
        }
        .demo-section { 
            margin: 2rem 0; 
            padding: 1rem; 
            border: 1px solid #ddd; 
            border-radius: 6px;
        }
        button { 
            padding: 0.75rem 1.5rem; 
            margin: 0.5rem; 
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
        }
        .btn-primary { background: #2196f3; color: white; }
        .btn-success { background: #4caf50; color: white; }
        .btn-warning { background: #ff9800; color: white; }
        .result { 
            margin: 1rem 0; 
            padding: 1rem; 
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; }
        h1 { color: #E53512; }
        h2 { color: #333; border-bottom: 2px solid #E53512; padding-bottom: 0.5rem; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎿 SnowNavi Feedback System - User Tracking Demo</h1>
        
        <div class="user-info">
            <h3>Current User Status</h3>
            <div id="current-user">Not logged in</div>
        </div>
        
        <div class="demo-section">
            <h2>Step 1: Simulate User Login</h2>
            <p>First, let's simulate different users logging in to see how their information is tracked.</p>
            <button class="btn-primary" onclick="loginAsUser('admin')">Login as Admin</button>
            <button class="btn-primary" onclick="loginAsUser('coach')">Login as Coach</button>
            <button class="btn-primary" onclick="loginAsUser('manager')">Login as Manager</button>
            <button class="btn-warning" onclick="logout()">Logout</button>
            <div id="login-result" class="result"></div>
        </div>
        
        <div class="demo-section">
            <h2>Step 2: Create Feedback Record</h2>
            <p>Create a feedback record to see how the current user's information is automatically recorded.</p>
            <button class="btn-success" onclick="createSampleFeedback()">Create Sample Feedback</button>
            <div id="feedback-result" class="result"></div>
        </div>
        
        <div class="demo-section">
            <h2>Step 3: View Feedback History</h2>
            <p>View all feedback records to see the user tracking information.</p>
            <button class="btn-primary" onclick="viewFeedbackHistory()">View All Feedbacks</button>
            <div id="history-result" class="result"></div>
        </div>
        
        <div class="demo-section">
            <h2>Step 4: Update Feedback</h2>
            <p>Update an existing feedback to see how update tracking works.</p>
            <button class="btn-warning" onclick="updateSampleFeedback()">Update Last Feedback</button>
            <div id="update-result" class="result"></div>
        </div>
    </div>

    <script>
        let feedbackCounter = 1;
        
        const users = {
            admin: {
                name: "John Smith",
                email: "<EMAIL>",
                picture: "https://via.placeholder.com/40/2196f3/ffffff?text=JS",
                role: "Administrator"
            },
            coach: {
                name: "Sarah Johnson",
                email: "<EMAIL>", 
                picture: "https://via.placeholder.com/40/4caf50/ffffff?text=SJ",
                role: "Head Coach"
            },
            manager: {
                name: "Mike Chen",
                email: "<EMAIL>",
                picture: "https://via.placeholder.com/40/ff9800/ffffff?text=MC",
                role: "Operations Manager"
            }
        };
        
        function updateUserDisplay() {
            const auth = JSON.parse(localStorage.getItem('snownavi_auth') || '{}');
            const userDiv = document.getElementById('current-user');
            
            if (auth.name) {
                userDiv.innerHTML = `
                    <strong>Name:</strong> ${auth.name}<br>
                    <strong>Email:</strong> ${auth.email}<br>
                    <strong>Role:</strong> ${auth.role || 'User'}<br>
                    <strong>Login Time:</strong> ${new Date(auth.loginTime).toLocaleString()}
                `;
            } else {
                userDiv.textContent = 'Not logged in';
            }
        }
        
        function loginAsUser(userType) {
            const user = users[userType];
            const auth = {
                ...user,
                loginTime: Date.now(),
                expiresAt: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
            };
            
            localStorage.setItem('snownavi_auth', JSON.stringify(auth));
            updateUserDisplay();
            
            const resultDiv = document.getElementById('login-result');
            resultDiv.className = 'result success';
            resultDiv.textContent = `✅ Successfully logged in as ${user.name} (${user.role})`;
        }
        
        function logout() {
            localStorage.removeItem('snownavi_auth');
            updateUserDisplay();
            
            const resultDiv = document.getElementById('login-result');
            resultDiv.className = 'result info';
            resultDiv.textContent = 'ℹ️ Logged out successfully';
        }
        
        function getCurrentUser() {
            const auth = JSON.parse(localStorage.getItem('snownavi_auth') || '{}');
            return {
                name: auth.name || 'Anonymous User',
                email: auth.email || '<EMAIL>',
                role: auth.role || 'Unknown'
            };
        }
        
        function createSampleFeedback() {
            const currentUser = getCurrentUser();
            const resultDiv = document.getElementById('feedback-result');
            
            if (!currentUser.name || currentUser.name === 'Anonymous User') {
                resultDiv.className = 'result warning';
                resultDiv.textContent = '⚠️ Please login first to create feedback';
                return;
            }
            
            const feedback = {
                id: `FB2025${feedbackCounter.toString().padStart(4, '0')}`,
                checkinId: `CHK2025${feedbackCounter.toString().padStart(4, '0')}`,
                activityId: "ACT20250001",
                memberId: "SN20250001",
                memberName: "Demo Student",
                feedback: `Great progress in lesson ${feedbackCounter}! Keep up the excellent work.`,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                createdBy: currentUser.name,
                createdByEmail: currentUser.email,
                createdByRole: currentUser.role
            };
            
            // Store in localStorage for demo
            const feedbacks = JSON.parse(localStorage.getItem('demo_feedbacks') || '{}');
            feedbacks[feedback.checkinId] = feedback;
            localStorage.setItem('demo_feedbacks', JSON.stringify(feedbacks));
            
            feedbackCounter++;
            
            resultDiv.className = 'result success';
            resultDiv.textContent = `✅ Feedback created successfully!\n\n${JSON.stringify(feedback, null, 2)}`;
        }
        
        function viewFeedbackHistory() {
            const feedbacks = JSON.parse(localStorage.getItem('demo_feedbacks') || '{}');
            const resultDiv = document.getElementById('history-result');
            
            if (Object.keys(feedbacks).length === 0) {
                resultDiv.className = 'result info';
                resultDiv.textContent = 'ℹ️ No feedback records found. Create some feedback first.';
                return;
            }
            
            resultDiv.className = 'result info';
            resultDiv.textContent = `📋 Feedback History (${Object.keys(feedbacks).length} records):\n\n${JSON.stringify(feedbacks, null, 2)}`;
        }
        
        function updateSampleFeedback() {
            const currentUser = getCurrentUser();
            const feedbacks = JSON.parse(localStorage.getItem('demo_feedbacks') || '{}');
            const resultDiv = document.getElementById('update-result');
            
            if (!currentUser.name || currentUser.name === 'Anonymous User') {
                resultDiv.className = 'result warning';
                resultDiv.textContent = '⚠️ Please login first to update feedback';
                return;
            }
            
            const feedbackKeys = Object.keys(feedbacks);
            if (feedbackKeys.length === 0) {
                resultDiv.className = 'result warning';
                resultDiv.textContent = '⚠️ No feedback records to update. Create some feedback first.';
                return;
            }
            
            // Update the last feedback
            const lastKey = feedbackKeys[feedbackKeys.length - 1];
            const feedback = feedbacks[lastKey];
            
            feedback.feedback += ' [UPDATED: Additional notes added by instructor]';
            feedback.updatedAt = new Date().toISOString();
            feedback.updatedBy = currentUser.name;
            feedback.updatedByEmail = currentUser.email;
            feedback.updatedByRole = currentUser.role;
            
            localStorage.setItem('demo_feedbacks', JSON.stringify(feedbacks));
            
            resultDiv.className = 'result success';
            resultDiv.textContent = `✅ Feedback updated successfully!\n\n${JSON.stringify(feedback, null, 2)}`;
        }
        
        // Initialize display
        updateUserDisplay();
    </script>
</body>
</html>
