<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate Preview - SnowNavi</title>
    <style>
        :root {
            --main-red: #E53512;
            --bg-light: #F9F4F3;
            --text-dark: #2F2F2F;
            --text-gray: #717171;
            --contrast-white: #FFFFFF;
            --accent-blue: #9ED4E7;
        }

        body { 
            font-family: 'Noto Sans SC', sans-serif; 
            margin: 2rem; 
            background: var(--bg-light);
            color: var(--text-dark);
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--contrast-white);
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 3px solid var(--main-red);
        }
        h1 { color: var(--main-red); }
        .preview-section {
            margin: 2rem 0;
            text-align: center;
        }
        .preview-canvas {
            border: 2px solid var(--main-red);
            border-radius: 8px;
            max-width: 100%;
            height: auto;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        .controls {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin: 2rem 0;
            flex-wrap: wrap;
        }
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        .control-group label {
            font-weight: bold;
            color: var(--main-red);
        }
        .control-group input, .control-group select {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        .btn {
            background: var(--main-red);
            color: var(--contrast-white);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 0.5rem;
        }
        .btn:hover {
            background: #c42e0f;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(229, 53, 18, 0.3);
        }
        .btn:disabled {
            background: var(--text-gray);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .layout-info {
            background: var(--bg-light);
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            border-left: 4px solid var(--main-red);
        }
        .layout-info h3 {
            color: var(--main-red);
            margin: 0 0 1rem 0;
        }
        .layout-list {
            list-style: none;
            padding: 0;
        }
        .layout-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(229, 53, 18, 0.1);
        }
        .layout-list li:last-child {
            border-bottom: none;
        }
        .section-number {
            display: inline-block;
            width: 24px;
            height: 24px;
            background: var(--main-red);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 24px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📜 SnowNavi Certificate Preview</h1>
            <p>Optimized layout with organized sections</p>
        </div>
        
        <div class="layout-info">
            <h3>🎨 New Certificate Layout</h3>
            <ul class="layout-list">
                <li><span class="section-number">1</span><strong>Brand Header:</strong> SnowNavi logo + name + tagline (both in red, at top)</li>
                <li><span class="section-number">2</span><strong>Certificate Title:</strong> "CERTIFICATE OF SNOWBOARDING ACHIEVEMENT"</li>
                <li><span class="section-number">3</span><strong>Member Info:</strong> "This certifies that" + Name + Member ID</li>
                <li><span class="section-number">4</span><strong>Course Info:</strong> Course name + date</li>
                <li><span class="section-number">5</span><strong>Skills Progress:</strong> Visual progress bars + completion statistics</li>
                <li><span class="section-number">6</span><strong>Member QR:</strong> Verification QR code</li>
                <li><span class="section-number">7</span><strong>Contact & Social:</strong> Email + website + social media QR codes (at bottom)</li>
                <li><span class="section-number">8</span><strong>Footer:</strong> Issue date + program description + copyright</li>
            </ul>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label for="student-name">Student Name:</label>
                <input type="text" id="student-name" value="Zhang Wei" placeholder="Enter student name">
            </div>
            
            <div class="control-group">
                <label for="member-id">Member ID:</label>
                <input type="text" id="member-id" value="SN20250001" placeholder="Enter member ID">
            </div>
            
            <div class="control-group">
                <label for="course-name">Course Name:</label>
                <input type="text" id="course-name" value="Beginner Snowboard Lesson" placeholder="Enter course name">
            </div>
            
            <div class="control-group">
                <label for="course-date">Course Date:</label>
                <input type="date" id="course-date" value="2025-01-27">
            </div>
            
            <div class="control-group">
                <label for="skill-level">Skill Level:</label>
                <select id="skill-level">
                    <option value="beginner">Beginner (5 skills)</option>
                    <option value="intermediate">Intermediate (12 skills)</option>
                    <option value="advanced" selected>Advanced (20 skills)</option>
                    <option value="expert">Expert (25 skills)</option>
                </select>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="generatePreview()">🎨 Generate Preview</button>
            <button class="btn" onclick="downloadCertificate()">📥 Download Certificate</button>
        </div>
        
        <div class="preview-section">
            <canvas id="preview-canvas" class="preview-canvas" style="display: none;"></canvas>
            <div id="preview-placeholder" style="padding: 3rem; color: var(--text-gray); border: 2px dashed #ddd; border-radius: 8px;">
                Click "Generate Preview" to see the new certificate layout
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 2rem; padding: 1.5rem; background: var(--bg-light); border-radius: 8px;">
            <h3 style="color: var(--main-red); margin-bottom: 1rem;">🎿 Use in Student Portal</h3>
            <a href="student_feedback.html" target="_blank" style="
                color: var(--contrast-white);
                text-decoration: none;
                font-weight: bold;
                font-size: 1.1rem;
                display: inline-block;
                padding: 0.75rem 1.5rem;
                background: var(--main-red);
                border-radius: 6px;
                transition: background-color 0.2s;
            ">Open Student Feedback Portal</a>
        </div>
    </div>

    <script src="certificate-generator.js"></script>
    <script>
        let certificateGenerator = new CertificateGenerator();
        
        function getSkillData(level) {
            const skillSets = {
                beginner: ['equipment-intro', 'single-foot-familiarity', 'single-foot-sliding', 'single-foot-climbing', 'single-foot-straight'],
                intermediate: ['equipment-intro', 'single-foot-familiarity', 'single-foot-sliding', 'single-foot-climbing', 'single-foot-straight', 'single-foot-heel-brake', 'single-foot-j-turn', 'static-gas-pedal', 'single-heel-side-push', 'single-toe-side-push', 'both-heel-side-push', 'both-toe-side-push'],
                advanced: ['equipment-intro', 'single-foot-familiarity', 'single-foot-sliding', 'single-foot-climbing', 'single-foot-straight', 'single-foot-heel-brake', 'single-foot-j-turn', 'static-gas-pedal', 'single-heel-side-push', 'single-toe-side-push', 'both-heel-side-push', 'both-toe-side-push', 'both-heel-falling-leaf', 'both-toe-falling-leaf', 'both-heel-power-falling-leaf', 'both-toe-power-falling-leaf', 'static-rotation', 'step-turns', 'j-turns', 'walking-edge-change'],
                expert: ['equipment-intro', 'single-foot-familiarity', 'single-foot-sliding', 'single-foot-climbing', 'single-foot-straight', 'single-foot-heel-brake', 'single-foot-j-turn', 'static-gas-pedal', 'single-heel-side-push', 'single-toe-side-push', 'both-heel-side-push', 'both-toe-side-push', 'both-heel-falling-leaf', 'both-toe-falling-leaf', 'both-heel-power-falling-leaf', 'both-toe-power-falling-leaf', 'static-rotation', 'step-turns', 'j-turns', 'walking-edge-change', 'beginner-turns', 'edge-change-traverse', 'traverse-body-movement', 'continuous-edge-change', 'scrub-360']
            };
            
            return {
                completedSkills: skillSets[level] || skillSets.beginner,
                sectionFeedbacks: {
                    basic: "Excellent foundation skills",
                    sliding: "Great progress on movement",
                    control: "Good edge control development",
                    turning: "Turning technique improving",
                    flow: "Advanced flow techniques mastered"
                }
            };
        }
        
        async function generatePreview() {
            const name = document.getElementById('student-name').value;
            const memberId = document.getElementById('member-id').value;
            const course = document.getElementById('course-name').value;
            const date = document.getElementById('course-date').value;
            const skillLevel = document.getElementById('skill-level').value;
            
            const certificateData = {
                memberName: name,
                memberId: memberId,
                activityName: course,
                activityDate: new Date(date).toLocaleDateString(),
                feedback: {
                    skillAssessment: getSkillData(skillLevel)
                }
            };
            
            try {
                const placeholder = document.getElementById('preview-placeholder');
                placeholder.innerHTML = '<div style="padding: 3rem; color: var(--text-gray);">🔄 Generating certificate with new layout...</div>';
                
                const canvas = await certificateGenerator.generateAdvancedCertificate(certificateData);
                
                // Show preview
                const previewCanvas = document.getElementById('preview-canvas');
                previewCanvas.width = canvas.width;
                previewCanvas.height = canvas.height;
                const previewCtx = previewCanvas.getContext('2d');
                previewCtx.drawImage(canvas, 0, 0);
                
                previewCanvas.style.display = 'block';
                placeholder.style.display = 'none';
                
            } catch (error) {
                console.error('Error generating certificate:', error);
                document.getElementById('preview-placeholder').innerHTML = 
                    '<div style="padding: 3rem; color: red;">❌ Error generating certificate. Please try again.</div>';
            }
        }
        
        async function downloadCertificate() {
            const name = document.getElementById('student-name').value;
            const memberId = document.getElementById('member-id').value;
            const course = document.getElementById('course-name').value;
            const date = document.getElementById('course-date').value;
            const skillLevel = document.getElementById('skill-level').value;
            
            const certificateData = {
                memberName: name,
                memberId: memberId,
                activityName: course,
                activityDate: new Date(date).toLocaleDateString(),
                feedback: {
                    skillAssessment: getSkillData(skillLevel)
                }
            };
            
            try {
                const canvas = await certificateGenerator.generateAdvancedCertificate(certificateData);
                
                canvas.toBlob((blob) => {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `SnowNavi_Certificate_${name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.jpg`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }, 'image/jpeg', 0.95);
                
            } catch (error) {
                console.error('Error downloading certificate:', error);
                alert('Failed to download certificate. Please try again.');
            }
        }
        
        // Generate initial preview
        window.onload = () => {
            setTimeout(generatePreview, 500);
        };
    </script>
</body>
</html>
