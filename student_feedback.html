<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Course Feedback - <PERSON><PERSON>avi</title>
    <link rel="icon" type="image/png" href="assets/picture/snownavi_logo.png">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 1rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #E53512 0%, #ff6b35 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .content {
            padding: 2rem;
        }

        /* Email Search Section */
        .search-section {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
        }

        .search-section h2 {
            color: #E53512;
            margin-bottom: 1rem;
        }

        .search-form {
            display: flex;
            gap: 1rem;
            max-width: 400px;
            margin: 0 auto;
        }

        .email-input {
            flex: 1;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;
        }

        .email-input:focus {
            outline: none;
            border-color: #E53512;
            box-shadow: 0 0 0 3px rgba(229, 53, 18, 0.1);
        }

        .search-btn {
            padding: 0.75rem 1.5rem;
            background: #E53512;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.2s;
        }

        .search-btn:hover {
            background: #c52e10;
        }

        .search-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        /* Course Selection */
        .course-selection {
            margin-bottom: 2rem;
            display: none;
        }

        .course-selection label {
            display: block;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .course-select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;
            background: white;
        }

        .course-select:focus {
            outline: none;
            border-color: #E53512;
            box-shadow: 0 0 0 3px rgba(229, 53, 18, 0.1);
        }

        /* Feedback Display */
        .feedback-display {
            display: none;
        }

        .student-info {
            background: #e3f2fd;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            border-left: 4px solid #2196f3;
        }

        .student-info h3 {
            color: #1976d2;
            margin-bottom: 0.5rem;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .info-item {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-weight: bold;
            color: #666;
            font-size: 0.9rem;
        }

        .info-value {
            color: #333;
            margin-top: 0.25rem;
        }

        /* Skill Assessment Display */
        .skill-assessment {
            margin-bottom: 2rem;
        }

        .skill-section {
            border: 2px solid #ddd;
            border-radius: 8px;
            margin-bottom: 1rem;
            overflow: hidden;
        }

        .skill-section.completed {
            border-color: #28a745;
            background-color: #f8fff9;
        }

        .section-header {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #ddd;
        }

        .skill-section.completed .section-header {
            background: #d4edda;
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: bold;
            color: #E53512;
            margin-bottom: 0.5rem;
        }

        .skill-section.completed .section-title {
            color: #28a745;
        }

        .progress-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .progress-text {
            font-size: 0.9rem;
            color: #666;
        }

        .progress-bar {
            flex: 1;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #28a745;
            transition: width 0.3s ease;
        }

        .skill-content {
            padding: 1rem;
        }

        .skill-items {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .skill-item {
            display: flex;
            align-items: center;
            padding: 0.5rem;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .skill-item.completed {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }

        .skill-icon {
            margin-right: 0.5rem;
            font-size: 1.2rem;
        }

        .skill-name {
            font-size: 0.9rem;
            line-height: 1.3;
        }

        .section-feedback {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 6px;
            margin-top: 1rem;
        }

        .section-feedback h4 {
            color: #E53512;
            margin-bottom: 0.5rem;
            font-size: 0.95rem;
        }

        .section-feedback-text {
            color: #555;
            line-height: 1.5;
            font-style: italic;
        }

        /* Overall Feedback */
        .overall-feedback {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .overall-feedback h3 {
            color: #856404;
            margin-bottom: 1rem;
        }

        .overall-feedback-text {
            color: #333;
            line-height: 1.6;
            font-size: 1.05rem;
        }

        /* Feedback Meta */
        .feedback-meta {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 6px;
            border-top: 3px solid #E53512;
            font-size: 0.9rem;
            color: #666;
        }

        .meta-item {
            margin-bottom: 0.5rem;
        }

        .meta-item:last-child {
            margin-bottom: 0;
        }

        /* Loading and Error States */
        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 6px;
            border: 1px solid #f5c6cb;
            margin: 1rem 0;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 6px;
            border: 1px solid #c3e6cb;
            margin: 1rem 0;
        }

        .no-feedback {
            text-align: center;
            padding: 3rem 2rem;
            color: #666;
        }

        .no-feedback h3 {
            color: #999;
            margin-bottom: 1rem;
        }

        /* Mobile Optimization */
        @media (max-width: 768px) {
            body {
                padding: 1rem 0.5rem;
            }

            .header {
                padding: 1.5rem 1rem;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .content {
                padding: 1rem;
            }

            .search-section {
                padding: 1.5rem 1rem;
            }

            .search-form {
                flex-direction: column;
                max-width: none;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }

            .skill-items {
                grid-template-columns: 1fr;
            }

            .progress-info {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .progress-bar {
                width: 100%;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.3rem;
            }

            .header p {
                font-size: 1rem;
            }

            .search-section {
                padding: 1rem;
            }

            .skill-content {
                padding: 0.75rem;
            }

            .section-header {
                padding: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎿 My Course Feedback</h1>
            <p>View your snowboarding progress and instructor feedback</p>
        </div>

        <div class="content">
            <!-- Email Search Section -->
            <div class="search-section">
                <h2>Find Your Feedback</h2>
                <p>Enter your email address to view your course feedback and progress</p>
                <div class="search-form">
                    <input 
                        type="email" 
                        id="email-input" 
                        class="email-input" 
                        placeholder="Enter your email address"
                        required
                    >
                    <button id="search-btn" class="search-btn" onclick="searchFeedback()">
                        Search
                    </button>
                </div>
                <div id="search-message"></div>
            </div>

            <!-- Course Selection -->
            <div id="course-selection" class="course-selection">
                <label for="course-select">Select Course:</label>
                <select id="course-select" class="course-select" onchange="displaySelectedFeedback()">
                    <option value="">Choose a course...</option>
                </select>
            </div>

            <!-- Feedback Display -->
            <div id="feedback-display" class="feedback-display">
                <!-- Content will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <script>
        let studentFeedbacks = [];
        let activities = {};
        let currentEmail = '';

        // Load activities data
        async function loadActivities() {
            try {
                const response = await fetch('data/activities.json');
                if (response.ok) {
                    activities = await response.json();
                }
            } catch (error) {
                console.error('Error loading activities:', error);
            }
        }

        async function searchFeedback() {
            const email = document.getElementById('email-input').value.trim().toLowerCase();
            const searchBtn = document.getElementById('search-btn');
            const messageDiv = document.getElementById('search-message');
            
            if (!email) {
                showMessage('Please enter your email address.', 'error');
                return;
            }

            // Validate email format
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showMessage('Please enter a valid email address.', 'error');
                return;
            }

            searchBtn.disabled = true;
            searchBtn.textContent = 'Searching...';
            messageDiv.innerHTML = '<div class="loading">🔍 Searching for your feedback...</div>';

            try {
                // Load feedbacks data
                const feedbackResponse = await fetch('data/feedbacks.json');
                if (!feedbackResponse.ok) {
                    throw new Error('Unable to load feedback data');
                }
                
                const allFeedbacks = await feedbackResponse.json();
                
                // Load members data to get member info by email
                const membersResponse = await fetch('data/members.json');
                if (!membersResponse.ok) {
                    throw new Error('Unable to load member data');
                }
                
                const members = await membersResponse.json();
                
                // Find member by email
                let memberInfo = null;
                let memberId = null;
                
                for (const [id, member] of Object.entries(members)) {
                    if (member.email && member.email.toLowerCase() === email) {
                        memberInfo = member;
                        memberId = id;
                        break;
                    }
                }
                
                if (!memberInfo) {
                    showMessage('No member found with this email address. Please check your email or contact support.', 'error');
                    return;
                }
                
                // Filter feedbacks for this member
                studentFeedbacks = [];
                for (const feedback of Object.values(allFeedbacks)) {
                    if (feedback.memberId === memberId) {
                        studentFeedbacks.push(feedback);
                    }
                }
                
                if (studentFeedbacks.length === 0) {
                    showMessage('No feedback found for this email address. Complete a course to receive feedback!', 'error');
                    return;
                }
                
                // Sort feedbacks by creation date (newest first)
                studentFeedbacks.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
                
                currentEmail = email;
                showMessage(`Found ${studentFeedbacks.length} feedback record(s) for ${memberInfo.name}!`, 'success');
                
                // Populate course selection
                populateCourseSelection();
                
                // Show course selection and display latest feedback
                document.getElementById('course-selection').style.display = 'block';
                displaySelectedFeedback();
                
            } catch (error) {
                console.error('Error searching feedback:', error);
                showMessage('Error searching for feedback. Please try again later.', 'error');
            } finally {
                searchBtn.disabled = false;
                searchBtn.textContent = 'Search';
            }
        }

        function populateCourseSelection() {
            const courseSelect = document.getElementById('course-select');
            courseSelect.innerHTML = '<option value="">Choose a course...</option>';
            
            studentFeedbacks.forEach((feedback, index) => {
                const activity = activities[feedback.activityId];
                const activityName = activity ? activity.name.en : 'Unknown Activity';
                const date = new Date(feedback.createdAt).toLocaleDateString();
                const isLatest = index === 0 ? ' (Latest)' : '';
                
                const option = document.createElement('option');
                option.value = index;
                option.textContent = `${activityName} - ${date}${isLatest}`;
                courseSelect.appendChild(option);
            });
            
            // Select the latest feedback by default
            courseSelect.value = '0';
        }

        function displaySelectedFeedback() {
            const courseSelect = document.getElementById('course-select');
            const selectedIndex = courseSelect.value;
            
            if (selectedIndex === '') {
                document.getElementById('feedback-display').style.display = 'none';
                return;
            }
            
            const feedback = studentFeedbacks[selectedIndex];
            if (!feedback) return;
            
            const activity = activities[feedback.activityId];
            const activityName = activity ? activity.name.en : 'Unknown Activity';
            const activityDate = activity ? new Date(activity.date).toLocaleDateString() : 'Unknown Date';
            
            const feedbackDisplay = document.getElementById('feedback-display');
            feedbackDisplay.innerHTML = generateFeedbackHTML(feedback, activityName, activityDate);
            feedbackDisplay.style.display = 'block';
        }

        function generateFeedbackHTML(feedback, activityName, activityDate) {
            let html = `
                <div class="student-info">
                    <h3>Course Information</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">Student Name</span>
                            <span class="info-value">${feedback.memberName}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Course</span>
                            <span class="info-value">${activityName}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Course Date</span>
                            <span class="info-value">${activityDate}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Feedback Date</span>
                            <span class="info-value">${new Date(feedback.createdAt).toLocaleDateString()}</span>
                        </div>
                    </div>
                </div>
            `;
            
            // Skill Assessment
            if (feedback.skillAssessment && feedback.skillAssessment.completedSkills) {
                html += generateSkillAssessmentHTML(feedback.skillAssessment);
            }
            
            // Overall Feedback
            const overallFeedback = feedback.overallFeedback || feedback.feedback || '';
            if (overallFeedback) {
                html += `
                    <div class="overall-feedback">
                        <h3>📝 Overall Course Feedback</h3>
                        <div class="overall-feedback-text">${overallFeedback}</div>
                    </div>
                `;
            }
            
            // Feedback Meta Information
            html += `
                <div class="feedback-meta">
                    <div class="meta-item"><strong>Instructor:</strong> ${feedback.createdBy || 'Unknown'}</div>
                    <div class="meta-item"><strong>Created:</strong> ${new Date(feedback.createdAt).toLocaleString()}</div>
                    ${feedback.updatedBy && feedback.updatedAt !== feedback.createdAt ? 
                        `<div class="meta-item"><strong>Last Updated:</strong> ${new Date(feedback.updatedAt).toLocaleString()} by ${feedback.updatedBy}</div>` : ''
                    }
                </div>
            `;
            
            return html;
        }

        function generateSkillAssessmentHTML(skillAssessment) {
            const skillSections = {
                basic: {
                    title: 'Basic 基础知识',
                    skills: {
                        'equipment-intro': '滑雪装备介绍 (Equipment Introduction)',
                        'single-foot-familiarity': '单脚熟悉雪板 (Single Foot Board Familiarity)'
                    }
                },
                sliding: {
                    title: 'Sliding 滑行',
                    skills: {
                        'single-foot-sliding': '单脚滑板式滑动 (Single Foot Skateboard Sliding)',
                        'single-foot-climbing': '单脚爬坡 (Single Foot Climbing)',
                        'single-foot-straight': '单脚直滑降 (Single Foot Straight Descent)',
                        'single-foot-heel-brake': '单脚脚后跟减速 (Single Foot Heel Braking)',
                        'single-foot-j-turn': '单脚J弯 (Single Foot J-Turn)'
                    }
                },
                control: {
                    title: 'Control 控制',
                    skills: {
                        'static-gas-pedal': '静态踩油门练习 (Static Gas Pedal Practice)',
                        'single-heel-side-push': '单脚后刃推坡 (Single Foot Heel Side Push)',
                        'single-toe-side-push': '单脚前刃推坡 (Single Foot Toe Side Push)',
                        'both-heel-side-push': '双脚后刃推坡 (Both Feet Heel Side Push)',
                        'both-toe-side-push': '双脚前刃推坡 (Both Feet Toe Side Push)',
                        'both-heel-falling-leaf': '双脚后刃落叶飘 (Both Feet Heel Side Falling Leaf)',
                        'both-toe-falling-leaf': '双脚前刃落叶飘 (Both Feet Toe Side Falling Leaf)',
                        'both-heel-power-falling-leaf': '双脚后刃强力落叶飘 (Both Feet Heel Side Power Falling Leaf)',
                        'both-toe-power-falling-leaf': '双脚前刃强力落叶飘 (Both Feet Toe Side Power Falling Leaf)'
                    }
                },
                turning: {
                    title: 'Turning 转弯',
                    skills: {
                        'static-rotation': '静态旋转练习 (Static Rotation Practice)',
                        'step-turns': '阶梯转弯 (Step Turns)',
                        'j-turns': 'J弯 (J-Turns)',
                        'walking-edge-change': '走步模拟换刃 (Walking Edge Change Simulation)',
                        'beginner-turns': '新手转弯 (Beginner Turns)'
                    }
                },
                flow: {
                    title: 'Flow 流畅性',
                    skills: {
                        'edge-change-traverse': '换刃后增加横穿雪道 (Edge Change with Slope Traverse)',
                        'traverse-body-movement': '横穿雪道加入身体起伏 (Traverse with Body Movement)',
                        'continuous-edge-change': '连续换刃 (Continuous Edge Changes)',
                        'scrub-360': '搓雪360 (Scrub 360)'
                    }
                }
            };
            
            let html = '<div class="skill-assessment"><h3>🎯 Skill Assessment</h3>';
            
            Object.entries(skillSections).forEach(([sectionId, section]) => {
                const sectionSkills = Object.keys(section.skills);
                const completedSkills = sectionSkills.filter(skill => 
                    skillAssessment.completedSkills.includes(skill)
                );
                const totalSkills = sectionSkills.length;
                const completedCount = completedSkills.length;
                const percentage = totalSkills > 0 ? (completedCount / totalSkills) * 100 : 0;
                const isCompleted = completedCount === totalSkills && totalSkills > 0;
                
                html += `
                    <div class="skill-section ${isCompleted ? 'completed' : ''}">
                        <div class="section-header">
                            <div class="section-title">${section.title}</div>
                            <div class="progress-info">
                                <span class="progress-text">${completedCount}/${totalSkills} completed</span>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${percentage}%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="skill-content">
                            <div class="skill-items">
                `;
                
                Object.entries(section.skills).forEach(([skillId, skillName]) => {
                    const isSkillCompleted = skillAssessment.completedSkills.includes(skillId);
                    html += `
                        <div class="skill-item ${isSkillCompleted ? 'completed' : ''}">
                            <span class="skill-icon">${isSkillCompleted ? '✅' : '⭕'}</span>
                            <span class="skill-name">${skillName}</span>
                        </div>
                    `;
                });
                
                html += '</div>';
                
                // Section feedback
                if (skillAssessment.sectionFeedbacks && skillAssessment.sectionFeedbacks[sectionId]) {
                    html += `
                        <div class="section-feedback">
                            <h4>Instructor Notes:</h4>
                            <div class="section-feedback-text">${skillAssessment.sectionFeedbacks[sectionId]}</div>
                        </div>
                    `;
                }
                
                html += '</div></div>';
            });
            
            html += '</div>';
            return html;
        }

        function showMessage(message, type) {
            const messageDiv = document.getElementById('search-message');
            messageDiv.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // Allow Enter key to trigger search
        document.getElementById('email-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchFeedback();
            }
        });

        // Load activities on page load
        window.onload = loadActivities;
    </script>
</body>
</html>
