<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate Layout Verification - SnowNavi</title>
    <style>
        :root {
            --main-red: #E53512;
            --bg-light: #F9F4F3;
            --text-dark: #2F2F2F;
            --text-gray: #717171;
            --contrast-white: #FFFFFF;
            --accent-blue: #9ED4E7;
        }

        body { 
            font-family: 'Noto Sans SC', sans-serif; 
            margin: 2rem; 
            background: var(--bg-light);
            color: var(--text-dark);
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--contrast-white);
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 3px solid var(--main-red);
        }
        h1 { color: var(--main-red); }
        .verification-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid var(--main-red);
            background: var(--bg-light);
        }
        .verification-section h3 {
            color: var(--main-red);
            margin: 0 0 1rem 0;
        }
        .check-list {
            list-style: none;
            padding: 0;
        }
        .check-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .check-icon {
            font-size: 1.2rem;
            width: 24px;
        }
        .layout-flow {
            background: var(--contrast-white);
            padding: 1.5rem;
            border-radius: 8px;
            margin: 1rem 0;
            border: 1px solid rgba(229, 53, 18, 0.2);
        }
        .flow-item {
            padding: 0.75rem;
            margin: 0.5rem 0;
            background: var(--bg-light);
            border-radius: 4px;
            border-left: 3px solid var(--main-red);
        }
        .flow-item strong {
            color: var(--main-red);
        }
        .btn {
            background: var(--main-red);
            color: var(--contrast-white);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 0.5rem;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #c42e0f;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(229, 53, 18, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✅ Certificate Layout Verification</h1>
            <p>Confirming optimized design without duplicates</p>
        </div>
        
        <div class="verification-section">
            <h3>🔍 Removed Duplicate Information</h3>
            <ul class="check-list">
                <li><span class="check-icon">✅</span>Removed duplicate SnowNavi logo (was in header and club section)</li>
                <li><span class="check-icon">✅</span>Removed duplicate "SnowNavi Snow Club" text</li>
                <li><span class="check-icon">✅</span>Removed duplicate "Professional Snowboarding Instruction Since 2021"</li>
                <li><span class="check-icon">✅</span>Consolidated member information flow</li>
            </ul>
        </div>
        
        <div class="verification-section">
            <h3>📋 New Layout Structure</h3>
            <div class="layout-flow">
                <div class="flow-item">
                    <strong>1. Header Section:</strong> Certificate title only - clean and focused
                </div>
                <div class="flow-item">
                    <strong>2. Member Information:</strong> "This certifies that" → Student Name → Member ID (logical flow)
                </div>
                <div class="flow-item">
                    <strong>3. Course Information:</strong> "has successfully participated in" → Course Name → Date
                </div>
                <div class="flow-item">
                    <strong>4. Skills Progress:</strong> Visual progress bars and completion statistics
                </div>
                <div class="flow-item">
                    <strong>5. Member Verification:</strong> QR code for member verification (no frame)
                </div>
                <div class="flow-item">
                    <strong>6. Club Information:</strong> SnowNavi logo + contact + social media QR codes (no frame)
                </div>
                <div class="flow-item">
                    <strong>7. Footer:</strong> Issue date + program description + copyright
                </div>
            </div>
        </div>
        
        <div class="verification-section">
            <h3>🎨 Design Improvements</h3>
            <ul class="check-list">
                <li><span class="check-icon">✅</span>Removed decorative frames/borders for cleaner look</li>
                <li><span class="check-icon">✅</span>Member info placed immediately after "This certifies that"</li>
                <li><span class="check-icon">✅</span>Eliminated redundant branding elements</li>
                <li><span class="check-icon">✅</span>Improved information hierarchy and flow</li>
                <li><span class="check-icon">✅</span>Maintained all essential information</li>
                <li><span class="check-icon">✅</span>Preserved QR code functionality</li>
            </ul>
        </div>
        
        <div class="verification-section">
            <h3>📱 QR Code Integration</h3>
            <ul class="check-list">
                <li><span class="check-icon">✅</span>Member QR code: Links to member.html?id=[memberID]</li>
                <li><span class="check-icon">✅</span>WeChat QR code: Real wechat_qrcode.jpg</li>
                <li><span class="check-icon">✅</span>Xiaohongshu QR code: Real xiaohongshu_qrcode.jpg</li>
                <li><span class="check-icon">✅</span>All QR codes properly sized and positioned</li>
            </ul>
        </div>
        
        <div class="verification-section">
            <h3>🎯 Information Flow</h3>
            <div style="background: var(--contrast-white); padding: 1rem; border-radius: 6px; font-family: monospace; font-size: 0.9rem;">
CERTIFICATE OF SNOWBOARDING ACHIEVEMENT<br><br>

This certifies that<br>
<strong style="color: var(--main-red);">[Student Name]</strong><br>
Member ID: [Member ID]<br><br>

has successfully participated in<br>
<strong style="color: var(--main-red);">[Course Name]</strong><br>
Course Date: [Date]<br><br>

Snowboarding Skills Progress<br>
[Progress Bars]<br>
Overall Progress: X/25 Skills (X%)<br><br>

Member Verification<br>
[QR Code]<br>
Scan to verify member<br><br>

[SnowNavi Logo]<br>
<strong style="color: var(--main-red);">SnowNavi Snow Club</strong><br>
Professional Snowboarding Instruction Since 2021<br>
Email: <EMAIL> | Website: snownavi.ski<br><br>

Follow Us<br>
[WeChat QR] [Xiaohongshu QR]<br><br>

Certificate issued on [Date]<br>
This certificate acknowledges participation and skill development<br>
in SnowNavi's professional snowboarding instruction program<br>
© 2025 SnowNavi Snow Club. All rights reserved.
            </div>
        </div>
        
        <div style="text-align: center; margin: 2rem 0;">
            <h3 style="color: var(--main-red); margin-bottom: 1rem;">🧪 Test the Updated Certificate</h3>
            <a href="certificate_preview.html" target="_blank" class="btn">🎨 Preview Certificate</a>
            <a href="student_feedback.html" target="_blank" class="btn">🎿 Student Portal</a>
        </div>
        
        <div style="background: var(--bg-light); padding: 1.5rem; border-radius: 8px; text-align: center;">
            <h4 style="color: var(--main-red); margin: 0 0 1rem 0;">✨ Optimization Complete</h4>
            <p>The certificate now has a clean, logical flow with no duplicate information. Member details appear immediately after "This certifies that" for better readability, and all decorative frames have been removed for a more professional appearance.</p>
        </div>
    </div>
</body>
</html>
